"""
Multi-Account Manager for Instagram Reels Autoposter
Handles multiple Instagram accounts with independent configurations
"""
import json
import os
import logging
import threading
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path

from config import ConfigManager
from auth import AuthManager
from posting_queue import QueueManager
from posting import PostingManager

logger = logging.getLogger(__name__)


@dataclass
class AccountConfig:
    """Configuration for a single Instagram account."""
    name: str
    username: str
    password: str = ""
    is_active: bool = True
    auto_post: bool = False
    post_schedule: str = "manual"  # manual, hourly, daily, custom
    custom_schedule: str = ""  # cron expression for custom schedule
    caption_mode: str = "manual"  # manual, ai_auto, ai_custom
    ai_caption_style: str = "engaging"  # engaging, professional, casual, funny
    max_posts_per_day: int = 10
    folder_path: str = ""
    proxy: Optional[str] = None
    created_at: str = ""
    last_active: str = ""
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()


class AccountInstance:
    """Complete instance of managers for a single account."""
    
    def __init__(self, account_config: AccountConfig):
        self.config = account_config
        self.account_id = self.config.name
        
        # Create account-specific file paths
        base_path = Path(f"accounts/{self.account_id}")
        base_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize account-specific managers
        self.config_manager = ConfigManager(str(base_path / "config.json"))
        self.auth_manager = AuthManager(str(base_path / "session.json"), self.config_manager)
        self.queue_manager = QueueManager(str(base_path / "posting_queue.json"))
        self.posting_manager = PostingManager(
            self.auth_manager.client, 
            self.config_manager, 
            self.queue_manager,
            self.account_id
        )
        
        # Initialize caption generator with config manager
        from caption_generator import CaptionGenerator
        self.caption_generator = CaptionGenerator(config_manager=self.config_manager)
        
        # Set account-specific configurations
        self._apply_account_config()
        
        # Status tracking
        self.is_logged_in = False
        self.last_error = ""
        self.posting_active = False
        self._account_lock = threading.Lock()  # Account-specific lock for thread safety
        
    def _apply_account_config(self):
        """Apply account configuration to managers."""
        # Update config manager with account settings
        self.config_manager.username = self.config.username
        self.config_manager.password = self.config.password
        self.config_manager.proxy = self.config.proxy
        self.config_manager.save_config()
        
        # Update posting manager rate limits
        self.posting_manager.max_posts_per_day = self.config.max_posts_per_day
        
        # Set proxy if configured
        if self.config.proxy:
            self.auth_manager.set_proxy(self.config.proxy)
    
    def login(self) -> bool:
        """Login to Instagram for this account."""
        with self._account_lock:
            try:
                success = self.auth_manager.login(self.config.username, self.config.password)
                self.is_logged_in = success
                if success:
                    self.config.last_active = datetime.now().isoformat()
                    logger.info(f"Account {self.account_id} logged in successfully")
                else:
                    self.last_error = "Login failed"
                    logger.error(f"Account {self.account_id} login failed")
                return success
            except Exception as e:
                self.last_error = str(e)
                logger.error(f"Account {self.account_id} login error: {e}")
                return False
    
    def verify_login(self) -> bool:
        """Verify login status for this account."""
        with self._account_lock:
            try:
                success = self.auth_manager.verify_login()
                self.is_logged_in = success
                return success
            except Exception as e:
                self.last_error = str(e)
                self.is_logged_in = False
                return False
    
    def safe_post_operation(self, operation_func, *args, **kwargs):
        """Execute a posting operation safely with account lock."""
        with self._account_lock:
            if not self.is_logged_in:
                raise Exception(f"Account {self.account_id} is not logged in")
            
            if self.posting_active:
                raise Exception(f"Account {self.account_id} is already posting")
            
            try:
                self.posting_active = True
                return operation_func(*args, **kwargs)
            finally:
                self.posting_active = False
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status for this account."""
        stats = self.posting_manager.get_posting_stats()
        rate_info = self.posting_manager.get_rate_limit_info()
        
        return {
            'account_id': self.account_id,
            'username': self.config.username,
            'is_logged_in': self.is_logged_in,
            'is_active': self.config.is_active,
            'auto_post': self.config.auto_post,
            'last_error': self.last_error,
            'posting_stats': stats,
            'rate_limit': rate_info,
            'queue_count': len(self.queue_manager.queue),
            'pending_count': stats.get('pending', 0),
            'folder_path': self.config.folder_path
        }


class AccountManager:
    """Manager for multiple Instagram accounts with enhanced scheduling and AI features."""
    
    def __init__(self, accounts_file: str = "accounts.json"):
        self.accounts_file = accounts_file
        self.accounts: Dict[str, AccountInstance] = {}
        self.active_account_id: Optional[str] = None
        self._lock = threading.Lock()
        
        # Create accounts directory
        os.makedirs("accounts", exist_ok=True)
        
        # Initialize global config manager for shared settings
        from config import ConfigManager
        self.global_config = ConfigManager("global_config.json")
        
        # Initialize global caption generator
        from caption_generator import CaptionGenerator
        self.global_caption_generator = CaptionGenerator(config_manager=self.global_config)
        
        # Initialize scheduling manager
        from scheduling import ScheduleManager
        self.schedule_manager = ScheduleManager(
            config_manager=self.global_config,
            account_manager=self
        )
        
        # Load existing accounts
        self.load_accounts()
    
    def load_accounts(self) -> None:
        """Load account configurations from file."""
        try:
            if os.path.exists(self.accounts_file):
                with open(self.accounts_file, 'r') as f:
                    accounts_data = json.load(f)
                
                for account_data in accounts_data.get('accounts', []):
                    account_config = AccountConfig(**account_data)
                    account_instance = AccountInstance(account_config)
                    self.accounts[account_config.name] = account_instance
                
                self.active_account_id = accounts_data.get('active_account')
                logger.info(f"Loaded {len(self.accounts)} accounts")
            else:
                logger.info("No accounts file found, starting fresh")
        except Exception as e:
            logger.error(f"Failed to load accounts: {e}")
    
    def save_accounts(self) -> None:
        """Save account configurations to file."""
        try:
            accounts_data = {
                'active_account': self.active_account_id,
                'accounts': []
            }
            
            for account_id, account_instance in self.accounts.items():
                account_data = asdict(account_instance.config)
                accounts_data['accounts'].append(account_data)
            
            with open(self.accounts_file, 'w') as f:
                json.dump(accounts_data, f, indent=2)
            
            logger.info(f"Saved {len(self.accounts)} accounts")
        except Exception as e:
            logger.error(f"Failed to save accounts: {e}")
    
    def add_account(self, name: str, username: str, password: str = "", **kwargs) -> bool:
        """Add a new Instagram account."""
        try:
            if name in self.accounts:
                logger.error(f"Account {name} already exists")
                return False
            
            account_config = AccountConfig(
                name=name,
                username=username,
                password=password,
                **kwargs
            )
            
            account_instance = AccountInstance(account_config)
            
            with self._lock:
                self.accounts[name] = account_instance
                if not self.active_account_id:
                    self.active_account_id = name
            
            self.save_accounts()
            logger.info(f"Added account: {name} ({username})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add account {name}: {e}")
            return False
    
    def remove_account(self, account_id: str) -> bool:
        """Remove an Instagram account."""
        try:
            if account_id not in self.accounts:
                logger.error(f"Account {account_id} not found")
                return False
            
            with self._lock:
                # Clean up account files
                account_path = Path(f"accounts/{account_id}")
                if account_path.exists():
                    import shutil
                    shutil.rmtree(account_path)
                
                # Remove from memory
                del self.accounts[account_id]
                
                # Update active account if necessary
                if self.active_account_id == account_id:
                    self.active_account_id = next(iter(self.accounts), None)
            
            self.save_accounts()
            logger.info(f"Removed account: {account_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove account {account_id}: {e}")
            return False
    
    def set_active_account(self, account_id: str) -> bool:
        """Set the active account."""
        if account_id in self.accounts:
            self.active_account_id = account_id
            self.save_accounts()
            logger.info(f"Set active account: {account_id}")
            return True
        return False
    
    def get_active_account(self) -> Optional[AccountInstance]:
        """Get the currently active account instance."""
        if self.active_account_id and self.active_account_id in self.accounts:
            return self.accounts[self.active_account_id]
        return None
    
    def get_account(self, account_id: str) -> Optional[AccountInstance]:
        """Get a specific account instance."""
        return self.accounts.get(account_id)
    
    def get_all_accounts(self) -> Dict[str, AccountInstance]:
        """Get all account instances."""
        return self.accounts.copy()
    
    def get_account_list(self) -> List[Dict[str, Any]]:
        """Get list of all accounts with their status."""
        accounts_list = []
        for account_id, account_instance in self.accounts.items():
            status = account_instance.get_status()
            accounts_list.append(status)
        return accounts_list
    
    def login_account(self, account_id: str) -> bool:
        """Login to a specific account."""
        account = self.get_account(account_id)
        if account:
            return account.login()
        return False
    
    def login_all_accounts(self) -> Dict[str, bool]:
        """Login to all active accounts."""
        results = {}
        for account_id, account_instance in self.accounts.items():
            if account_instance.config.is_active:
                results[account_id] = account_instance.login()
            else:
                results[account_id] = False
        return results
    
    def verify_all_accounts(self) -> Dict[str, bool]:
        """Verify login status for all accounts."""
        results = {}
        for account_id, account_instance in self.accounts.items():
            results[account_id] = account_instance.verify_login()
        return results
    
    def update_account_config(self, account_id: str, updates: Dict[str, Any]) -> bool:
        """Update account configuration."""
        try:
            account = self.get_account(account_id)
            if not account:
                return False
            
            # Update config
            for key, value in updates.items():
                if hasattr(account.config, key):
                    setattr(account.config, key, value)
            
            # Re-apply configuration
            account._apply_account_config()
            self.save_accounts()
            
            logger.info(f"Updated account {account_id} configuration")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update account {account_id}: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        total_accounts = len(self.accounts)
        logged_in_accounts = sum(1 for acc in self.accounts.values() if acc.is_logged_in)
        active_accounts = sum(1 for acc in self.accounts.values() if acc.config.is_active)
        auto_post_accounts = sum(1 for acc in self.accounts.values() if acc.config.auto_post)
        
        total_queue = sum(len(acc.queue_manager.queue) for acc in self.accounts.values())
        total_pending = sum(acc.get_status()['pending_count'] for acc in self.accounts.values())
        
        return {
            'total_accounts': total_accounts,
            'logged_in_accounts': logged_in_accounts,
            'active_accounts': active_accounts,
            'auto_post_accounts': auto_post_accounts,
            'total_queue_items': total_queue,
            'total_pending_items': total_pending,
            'active_account_id': self.active_account_id
        }
    
    def start_concurrent_posting(self, account_ids: List[str] = None) -> Dict[str, bool]:
        """Start posting for multiple accounts concurrently."""
        if account_ids is None:
            account_ids = [acc_id for acc_id, acc in self.accounts.items() 
                          if acc.config.is_active and acc.is_logged_in]
        
        results = {}
        threads = []
        
        def post_for_account(account_id: str) -> None:
            """Post from a single account."""
            try:
                account = self.get_account(account_id)
                if not account:
                    results[account_id] = False
                    return
                
                # Check if account has items to post
                pending_items = [item for item in account.queue_manager.queue 
                               if item.get('status') == 'pending']
                
                if not pending_items:
                    logger.info(f"No pending items for account {account_id}")
                    results[account_id] = True
                    return
                
                # Start posting for this account
                for item in pending_items[:1]:  # Post one item at a time to avoid rate limits
                    try:
                        account.safe_post_operation(
                            account.posting_manager.post_reel,
                            item,
                            dry_run=False
                        )
                        results[account_id] = True
                        logger.info(f"Successfully posted from account {account_id}")
                        break
                    except Exception as e:
                        logger.error(f"Failed to post from account {account_id}: {e}")
                        results[account_id] = False
                        
            except Exception as e:
                logger.error(f"Error in concurrent posting for {account_id}: {e}")
                results[account_id] = False
        
        # Start threads for each account
        for account_id in account_ids:
            thread = threading.Thread(target=post_for_account, args=(account_id,))
            thread.daemon = True
            threads.append(thread)
            thread.start()
            
            # Small delay between starting threads to avoid Instagram rate limiting
            time.sleep(1)
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=300)  # 5 minute timeout per account
        
        logger.info(f"Concurrent posting completed. Results: {results}")
        return results
