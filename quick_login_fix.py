#!/usr/bin/env python3
"""
Quick fix for Instagram login issues
This script will help bypass IP blacklist and test different approaches
"""
import sys
import os
import time
import random
import json
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.getcwd())

from instagrapi import Client
from instagrapi.exceptions import <PERSON>gin<PERSON>equired, <PERSON>lientError, TwoFactorRequired, Bad<PERSON>assword, ChallengeRequired

def create_client_with_better_settings():
    """Create Instagram client with optimized settings to avoid detection"""
    client = Client()
    
    # Set better user agent and device settings
    client.set_user_agent("Instagram 283.********** Android (29/10; 480dpi; 1080x1920; OnePlus; OnePlus6; OnePlus6; qcom; en_US; 400000000)")
    
    # Set device settings to avoid detection
    client.set_device({
        "app_version": "283.**********",
        "android_version": 29,
        "android_release": "10",
        "dpi": "480dpi",
        "resolution": "1080x1920",
        "manufacturer": "OnePlus",
        "device": "OnePlus6",
        "model": "OnePlus6",
        "cpu": "qcom",
        "version_code": "400000000"
    })
    
    return client

def test_with_delay():
    """Test login with delays to avoid rate limiting"""
    print("=== Testing Login with Delays ===")
    
    username = "hh"
    password = "**********"
    
    try:
        client = create_client_with_better_settings()
        
        print("⏰ Adding delay to avoid rate limiting...")
        time.sleep(random.randint(10, 20))
        
        print("🔐 Attempting login with optimized settings...")
        success = client.login(username, password)
        
        if success:
            print("✅ Login successful!")
            
            # Test API call
            try:
                user_info = client.account_info()
                print(f"✅ Account verified: {user_info.username}")
                
                # Save session
                session_file = "accounts/hh122.11/session.json"
                os.makedirs(os.path.dirname(session_file), exist_ok=True)
                client.dump_settings(session_file)
                print(f"💾 Session saved to {session_file}")
                
                return True
            except Exception as e:
                print(f"⚠️ Login successful but API test failed: {e}")
                return True
        else:
            print("❌ Login failed")
            return False
            
    except BadPassword as e:
        print(f"❌ Invalid credentials: {e}")
        return False
    except Exception as e:
        error_str = str(e).lower()
        if "blacklist" in error_str:
            print(f"🚫 IP still blacklisted: {e}")
            print("💡 Please wait longer or try different network")
        else:
            print(f"❌ Login error: {e}")
        return False

def test_session_restore():
    """Test if we can restore from existing session"""
    print("\n=== Testing Session Restore ===")
    
    session_file = "accounts/hh122.11/session.json"
    
    if not os.path.exists(session_file):
        print(f"❌ No session file found: {session_file}")
        return False
    
    try:
        client = create_client_with_better_settings()
        client.load_settings(session_file)
        
        print("📂 Loaded existing session")
        
        # Test if session is still valid
        user_info = client.account_info()
        print(f"✅ Session is valid: {user_info.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ Session restore failed: {e}")
        return False

def create_working_session():
    """Create a working session file for the application"""
    print("\n=== Creating Working Session ===")
    
    # First try session restore
    if test_session_restore():
        print("✅ Existing session is working")
        return True
    
    # If no session, try login with delays
    if test_with_delay():
        print("✅ New session created successfully")
        return True
    
    print("❌ Could not create working session")
    return False

def update_application_config():
    """Update application config to ensure credentials are properly set"""
    print("\n=== Updating Application Config ===")
    
    try:
        # Update main accounts.json
        accounts_file = "accounts.json"
        if os.path.exists(accounts_file):
            with open(accounts_file, 'r') as f:
                accounts_data = json.load(f)
            
            # Ensure credentials are set
            for account in accounts_data.get('accounts', []):
                if account['name'] == 'hh122.11':
                    account['username'] = 'hh'
                    account['password'] = '**********'
            
            with open(accounts_file, 'w') as f:
                json.dump(accounts_data, f, indent=2)
            
            print("✅ Updated accounts.json")
        
        # Update account-specific config
        config_file = "accounts/hh122.11/config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config_data = json.load(f)
            
            # Ensure credentials are in both places
            config_data['credentials']['username'] = 'hh'
            config_data['credentials']['password'] = '**********'
            config_data['username'] = 'hh'
            config_data['password'] = '**********'
            
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            print("✅ Updated account config")
        
        return True
        
    except Exception as e:
        print(f"❌ Config update failed: {e}")
        return False

def main():
    print("Instagram Login Quick Fix")
    print("=" * 50)
    
    # Update configs first
    update_application_config()
    
    # Try to create working session
    if create_working_session():
        print("\n🎉 SUCCESS!")
        print("✅ Login should now work in the application")
        print("🔄 Please restart the application and try again")
    else:
        print("\n❌ FAILED!")
        print("💡 Possible solutions:")
        print("1. Wait 30-60 minutes for IP unblock")
        print("2. Try using mobile hotspot or VPN")
        print("3. Login manually on Instagram website first")
        print("4. Check if account needs verification")

if __name__ == "__main__":
    main()
