"""
Modern Multi-Account Instagram Autoposter GUI
Enhanced UI/UX with account management, AI captions, and background operations
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
from tkinter.scrolledtext import ScrolledText
import threading
import queue
import time
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import partial
import weakref

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import pystray
    from pystray import MenuItem, Menu
    PYSTRAY_AVAILABLE = True
except ImportError:
    PYSTRAY_AVAILABLE = False

from account_manager import AccountManager, AccountInstance, AccountConfig
from caption_generator import CaptionGenerator
from messaging import InstagramMessaging, DiscoveredUser, MessageType, ScheduleType


class ModernAutoposterGUI:
    """Modern multi-account Instagram autoposter with enhanced UI/UX."""

    def __init__(self, root):
        self.root = root
        self.root.title("Instagram Multi-Account Autoposter v3.0")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)

        # Threading and performance optimization
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="GUI_Worker")
        self.gui_update_queue = queue.Queue()
        self.is_destroyed = False
        self.debounce_timers = {}  # For debouncing operations

        # Core managers
        self.account_manager = AccountManager()
        self.caption_generator = self.account_manager.global_caption_generator
        self.schedule_manager = self.account_manager.schedule_manager
        self.global_config = self.account_manager.global_config

        # Messaging system (initialized when account is selected)
        self.messaging = None

        # GUI state
        self.log_queue = queue.Queue()
        self.selected_files = []
        self.current_account = None

        # System tray
        self.tray_icon = None
        self.is_minimized_to_tray = False

        # Performance optimization caches
        self._file_list_cache = {}
        self._user_list_cache = {}
        self._last_update_time = {}
        
        # Initialize variables that will be used in GUI setup
        self.api_key_var = tk.StringVar()
        self.openrouter_model_var = tk.StringVar(value=self.global_config.openrouter_default_model)
        self.fallback_model_var = tk.StringVar(value=self.global_config.openrouter_fallback_model)
        self.caption_style_var = tk.StringVar(value=self.global_config.caption_default_style)
        self.auto_generate_var = tk.BooleanVar(value=self.global_config.auto_generate_captions)
        self.schedule_type_var = tk.StringVar(value="draft")
        
        # Setup GUI
        self.setup_styles()
        self.setup_gui()
        self.setup_logging()
        self.setup_system_tray()
        
        # Load initial data
        self.refresh_account_list()
        self.auto_select_account()

        # Initialize displays
        self.update_dashboard()
        self.update_content_list()
        self.update_posting_controls()
        self.update_system_stats()
        
        # Auto-restore sessions for all accounts
        self._auto_restore_sessions()
        
        # Auto-login if enabled and credentials available
        self._auto_login_check()

        # Set initial API key if available
        if self.caption_generator.api_key:
            self.api_key_var.set(self.caption_generator.api_key)
        
        # Background scheduler
        self.scheduler_thread = None
        self.scheduler_active = False

        # Initialize background service
        self.background_service = None
        self._init_background_service()
        
        # Session management
        self.session_refresh_timer = None
        self._start_session_refresh_timer()

        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Start GUI update processing
        self._start_gui_update_processor()

        # Create debounced methods to prevent excessive search operations
        self.debounced_search_hashtag = self.debounce('hashtag_search', self._debounced_search_hashtag, delay_ms=500)
        self.debounced_search_username = self.debounce('username_search', self._debounced_search_username, delay_ms=500)

    def _start_gui_update_processor(self):
        """Start the GUI update processor thread."""
        def process_updates():
            while not self.is_destroyed:
                try:
                    # Process GUI updates with timeout to avoid blocking
                    update_func, args, kwargs = self.gui_update_queue.get(timeout=0.1)
                    if update_func and not self.is_destroyed:
                        try:
                            update_func(*args, **kwargs)
                        except Exception as e:
                            print(f"GUI update error: {e}")
                except queue.Empty:
                    continue
                except Exception as e:
                    if not self.is_destroyed:
                        print(f"GUI update processor error: {e}")

        self.update_thread = threading.Thread(target=process_updates, daemon=True, name="GUI_Update_Processor")
        self.update_thread.start()

    def safe_update_gui(self, update_func, *args, **kwargs):
        """Thread-safe GUI update using queue."""
        if not self.is_destroyed:
            self.gui_update_queue.put((update_func, args, kwargs))

    def debounce(self, key, func, delay_ms=300):
        """Debounce function calls to prevent excessive execution."""
        def debounced(*args, **kwargs):
            # Cancel previous timer
            if key in self.debounce_timers:
                self.root.after_cancel(self.debounce_timers[key])

            # Schedule new execution
            self.debounce_timers[key] = self.root.after(delay_ms, lambda: func(*args, **kwargs))

        return debounced

    def run_in_background(self, func, callback=None, *args, **kwargs):
        """Run a function in background thread with optional callback."""
        def background_task():
            try:
                result = func(*args, **kwargs)
                if callback and not self.is_destroyed:
                    self.safe_update_gui(callback, result)
            except Exception as e:
                print(f"Background task error: {e}")
                if callback and not self.is_destroyed:
                    self.safe_update_gui(callback, None, error=str(e))

        self.executor.submit(background_task)

    def setup_styles(self):
        """Setup modern UI styles."""
        style = ttk.Style()
        
        # Configure modern theme
        style.theme_use('clam')
        
        # Custom styles
        style.configure('Title.TLabel', font=('Segoe UI', 16, 'bold'))
        style.configure('Subtitle.TLabel', font=('Segoe UI', 12, 'bold'))
        style.configure('Header.TLabel', font=('Segoe UI', 11, 'bold'))
        style.configure('Status.TLabel', font=('Segoe UI', 9))
        
        # Button styles
        style.configure('Primary.TButton', font=('Segoe UI', 10, 'bold'))
        style.configure('Success.TButton', font=('Segoe UI', 10))
        style.configure('Danger.TButton', font=('Segoe UI', 10))
        style.configure('Warning.TButton', font=('Segoe UI', 10))
        
        # Colors
        style.configure('Primary.TButton', background='#007ACC', foreground='white')
        style.configure('Success.TButton', background='#28A745', foreground='white')
        style.configure('Danger.TButton', background='#DC3545', foreground='white')
        style.configure('Warning.TButton', background='#FFC107', foreground='black')

    def setup_gui(self):
        """Setup the main GUI layout."""
        # Main container with padding
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Top header
        self.create_header(main_frame)
        
        # Main content with paned window
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Left sidebar (30%)
        left_panel = ttk.Frame(paned, width=350)
        paned.add(left_panel, weight=3)
        self.create_sidebar(left_panel)
        
        # Right main area (70%)
        right_panel = ttk.Frame(paned)
        paned.add(right_panel, weight=7)
        self.create_main_area(right_panel)

    def create_header(self, parent):
        """Create modern header with status and controls."""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Left side - Title and status
        left_header = ttk.Frame(header_frame)
        left_header.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        ttk.Label(left_header, text="Instagram Multi-Account Autoposter", 
                 style='Title.TLabel').pack(anchor='w')
        
        self.system_status = ttk.Label(left_header, text="Loading...", style='Status.TLabel')
        self.system_status.pack(anchor='w', pady=(2, 0))
        
        # Right side - Quick actions
        right_header = ttk.Frame(header_frame)
        right_header.pack(side=tk.RIGHT)
        
        ttk.Button(right_header, text="⚙️ Settings", command=self.open_settings,
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(right_header, text="🔄 Refresh All", command=self.refresh_all,
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(right_header, text="➕ Add Account", command=self.add_account_dialog,
                  style='Success.TButton').pack(side=tk.RIGHT, padx=(5, 0))

    def create_sidebar(self, parent):
        """Create sidebar with account management."""
        # Account list section
        accounts_frame = ttk.LabelFrame(parent, text="📱 Instagram Accounts", padding="10")
        accounts_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Account list with scrollbar
        list_frame = ttk.Frame(accounts_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.accounts_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set,
                                          font=('Segoe UI', 10), height=8)
        self.accounts_listbox.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.accounts_listbox.yview)
        
        self.accounts_listbox.bind('<<ListboxSelect>>', self.on_account_select)
        
        # Account controls
        account_controls = ttk.Frame(accounts_frame)
        account_controls.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(account_controls, text="🔑 Login", command=self.login_account,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(account_controls, text="✅ Verify", command=self.verify_account,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(account_controls, text="🔄 Refresh", command=self.refresh_current_session,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(account_controls, text="🗑️ Remove", command=self.remove_account,
                  style='Danger.TButton').pack(side=tk.RIGHT)
        
        # Quick stats
        self.create_quick_stats(parent)

    def create_quick_stats(self, parent):
        """Create quick statistics panel."""
        stats_frame = ttk.LabelFrame(parent, text="📊 Quick Stats", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.stats_labels = {}
        
        # Create stat rows
        stats = [
            ("accounts", "Total Accounts", "0"),
            ("logged_in", "Logged In", "0"),
            ("posting", "Auto-Posting", "0"),
            ("queue", "Total Queue", "0")
        ]
        
        for i, (key, label, default) in enumerate(stats):
            row = ttk.Frame(stats_frame)
            row.pack(fill=tk.X, pady=2)
            
            ttk.Label(row, text=f"{label}:", font=('Segoe UI', 9)).pack(side=tk.LEFT)
            self.stats_labels[key] = ttk.Label(row, text=default, 
                                              font=('Segoe UI', 9, 'bold'))
            self.stats_labels[key].pack(side=tk.RIGHT)

    def create_main_area(self, parent):
        """Create main content area with tabs."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Account Dashboard Tab
        self.dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.dashboard_frame, text="📊 Dashboard")
        self.create_dashboard_tab()
        
        # Content Management Tab
        self.content_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.content_frame, text="📁 Content")
        self.create_content_tab()
        
        # AI Captions Tab
        self.captions_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.captions_frame, text="🤖 AI Captions")
        self.create_captions_tab()
        
        # Posting Tab
        self.posting_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.posting_frame, text="📤 Posting")
        self.create_posting_tab()

        # Send Messages Tab
        self.messages_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.messages_frame, text="💬 Send Messages")
        self.create_messages_tab()

        # Logs Tab
        self.logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.logs_frame, text="📋 Activity Logs")
        self.create_logs_tab()

    def create_dashboard_tab(self):
        """Create account dashboard tab."""
        # Account info section
        info_frame = ttk.LabelFrame(self.dashboard_frame, text="Account Information", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Account details
        self.account_info_frame = ttk.Frame(info_frame)
        self.account_info_frame.pack(fill=tk.X)
        
        self.account_info_labels = {}
        
        # Posting statistics
        posting_frame = ttk.LabelFrame(self.dashboard_frame, text="Posting Statistics", padding="10")
        posting_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create statistics display
        self.posting_stats_frame = ttk.Frame(posting_frame)
        self.posting_stats_frame.pack(fill=tk.BOTH, expand=True)

    def create_content_tab(self):
        """Create content management tab."""
        # File selection section
        selection_frame = ttk.LabelFrame(self.content_frame, text="📁 File Selection", padding="10")
        selection_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Folder selection
        folder_frame = ttk.Frame(selection_frame)
        folder_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(folder_frame, text="Content Folder:").pack(side=tk.LEFT)
        self.folder_var = tk.StringVar()
        folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var, width=50)
        folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 5))
        ttk.Button(folder_frame, text="Browse", command=self.browse_folder).pack(side=tk.RIGHT)
        
        # File filter options
        filter_frame = ttk.Frame(selection_frame)
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(filter_frame, text="Filter:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="all")
        ttk.Radiobutton(filter_frame, text="All Videos", variable=self.filter_var, 
                       value="all").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Radiobutton(filter_frame, text="MP4 Only", variable=self.filter_var, 
                       value="mp4").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Radiobutton(filter_frame, text="MOV Only", variable=self.filter_var, 
                       value="mov").pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(filter_frame, text="🔄 Scan Folder", command=self.scan_folder,
                  style='Primary.TButton').pack(side=tk.RIGHT)
        
        # File list
        files_frame = ttk.LabelFrame(self.content_frame, text="📋 Content Queue", padding="10")
        files_frame.pack(fill=tk.BOTH, expand=True)
        
        # File list with details
        self.create_file_list(files_frame)

    def create_file_list(self, parent):
        """Create enhanced file list with preview and proper checkboxes."""
        # Treeview for file list
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Treeview with checkbox column included in regular columns
        columns = ("selected", "filename", "size", "status", "caption")
        self.files_tree = ttk.Treeview(tree_frame, columns=columns, show="headings",
                                      yscrollcommand=v_scrollbar.set,
                                      xscrollcommand=h_scrollbar.set)

        # Configure scrollbars
        v_scrollbar.config(command=self.files_tree.yview)
        h_scrollbar.config(command=self.files_tree.xview)

        # Column headers
        self.files_tree.heading("selected", text="✓", anchor='center')
        self.files_tree.heading("filename", text="Filename")
        self.files_tree.heading("size", text="Size")
        self.files_tree.heading("status", text="Status")
        self.files_tree.heading("caption", text="Caption Preview")

        # Column widths
        self.files_tree.column("selected", width=50, minwidth=50, anchor='center')
        self.files_tree.column("filename", width=200, minwidth=150)
        self.files_tree.column("size", width=80, minwidth=60)
        self.files_tree.column("status", width=80, minwidth=60)
        self.files_tree.column("caption", width=300, minwidth=200)

        self.files_tree.pack(fill=tk.BOTH, expand=True)

        # Bind events - simplified approach
        self.files_tree.bind('<Double-1>', self._on_tree_double_click)
        self.files_tree.bind('<Button-1>', self._on_tree_single_click)
        self.files_tree.bind('<<TreeviewSelect>>', self._on_file_select)

        # Store item mappings
        self.item_to_index = {}

        # Configure tags for visual feedback
        self.files_tree.tag_configure('selected', background='#e8f5e8', foreground='#2d5a2d')  # Light green background
        self.files_tree.tag_configure('unselected', background='#ffffff', foreground='#000000')  # Normal background
        self.files_tree.tag_configure('even', background='#f9f9f9')
        self.files_tree.tag_configure('odd', background='#ffffff')

        # File controls
        file_controls = ttk.Frame(parent)
        file_controls.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(file_controls, text="✅ Select All", command=self.select_all_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_controls, text="❌ Deselect All", command=self.deselect_all_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_controls, text="🔄 Invert Selection", command=self.invert_selection).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_controls, text="⏳ Select Pending", command=self.select_pending_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_controls, text="🗑️ Remove Selected", command=self.remove_selected_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_controls, text="📝 Edit Caption", command=self.edit_caption_dialog).pack(side=tk.RIGHT)

        # Selection status bar and instructions
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(5, 0))

        self.selection_status_label = ttk.Label(status_frame, text="No files loaded",
                                               style='Status.TLabel')
        self.selection_status_label.pack(side=tk.LEFT)

        # Instructions label
        instructions_label = ttk.Label(status_frame, 
                                     text="💡 Click ☐/☑ to select/deselect • Double-click to edit caption",
                                     style='Info.TLabel')
        instructions_label.pack(side=tk.RIGHT)

        # Update status when content list changes
        self._update_selection_status()

    def create_captions_tab(self):
        """Create enhanced AI captions management tab with OpenRouter integration."""
        # API Configuration
        api_frame = ttk.LabelFrame(self.captions_frame, text="🤖 OpenRouter AI Configuration", padding="10")
        api_frame.pack(fill=tk.X, pady=(0, 10))
        
        # API Key section
        key_frame = ttk.Frame(api_frame)
        key_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(key_frame, text="OpenRouter API Key:").pack(side=tk.LEFT)
        api_entry = ttk.Entry(key_frame, textvariable=self.api_key_var, show="*", width=40)
        api_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 5))
        ttk.Button(key_frame, text="💾 Save", command=self.save_api_key,
                  style='Success.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(key_frame, text="🔄 Test", command=self.test_api_connection,
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        
        # Model selection section
        model_frame = ttk.Frame(api_frame)
        model_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Primary model
        ttk.Label(model_frame, text="Primary Model:").grid(row=0, column=0, sticky='w', padx=(0, 10))
        primary_model_combo = ttk.Combobox(model_frame, textvariable=self.openrouter_model_var,
                                          values=list(self.global_config.get_openrouter_models().keys()),
                                          state="readonly", width=40)
        primary_model_combo.grid(row=0, column=1, sticky='ew', padx=(0, 10))
        
        # Fallback model
        ttk.Label(model_frame, text="Fallback Model:").grid(row=1, column=0, sticky='w', padx=(0, 10), pady=(5, 0))
        fallback_model_combo = ttk.Combobox(model_frame, textvariable=self.fallback_model_var,
                                           values=list(self.global_config.get_openrouter_models().keys()),
                                           state="readonly", width=40)
        fallback_model_combo.grid(row=1, column=1, sticky='ew', padx=(0, 10), pady=(5, 0))
        
        # Refresh models button
        ttk.Button(model_frame, text="🔄 Refresh Models", command=self.refresh_models,
                  style='Primary.TButton').grid(row=0, column=2, rowspan=2, padx=(5, 0))
        
        model_frame.columnconfigure(1, weight=1)
        
        # Caption style and settings
        settings_frame = ttk.Frame(api_frame)
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(settings_frame, text="Default Style:").pack(side=tk.LEFT)
        style_combo = ttk.Combobox(settings_frame, textvariable=self.caption_style_var,
                                  values=list(self.caption_generator.get_available_styles().keys()),
                                  state="readonly", width=15)
        style_combo.pack(side=tk.LEFT, padx=(10, 20))
        
        # Auto-generate option
        ttk.Checkbutton(settings_frame, text="Auto-generate on folder scan", 
                       variable=self.auto_generate_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # Bulk operations section
        bulk_frame = ttk.LabelFrame(api_frame, text="📊 Bulk Operations", padding="5")
        bulk_frame.pack(fill=tk.X, pady=(10, 0))
        
        bulk_buttons_frame = ttk.Frame(bulk_frame)
        bulk_buttons_frame.pack(fill=tk.X)
        
        ttk.Button(bulk_buttons_frame, text="🔮 Generate All Captions", command=self.generate_all_captions,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(bulk_buttons_frame, text="✨ Generate Selected Only", command=self.generate_selected_captions,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(bulk_buttons_frame, text="🧹 Clear Cache", command=self.clear_caption_cache,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(bulk_buttons_frame, text="⚙️ Save Settings", command=self.save_caption_settings,
                  style='Success.TButton').pack(side=tk.RIGHT)
        
        # Caption preview and editing
        preview_frame = ttk.LabelFrame(self.captions_frame, text="📝 Caption Preview & Edit", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True)
        
        # Selected file info
        file_info_frame = ttk.Frame(preview_frame)
        file_info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.selected_file_label = ttk.Label(file_info_frame, text="No file selected", 
                                           style='Header.TLabel')
        self.selected_file_label.pack(side=tk.LEFT)
        
        # Caption generation controls
        gen_frame = ttk.Frame(file_info_frame)
        gen_frame.pack(side=tk.RIGHT)
        
        ttk.Button(gen_frame, text="🔮 Generate", command=self.generate_single_caption,
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        
        style_single = ttk.Combobox(gen_frame, textvariable=self.caption_style_var,
                                   values=list(self.caption_generator.get_available_styles().keys()),
                                   state="readonly", width=12)
        style_single.pack(side=tk.RIGHT, padx=(5, 0))
        
        # Caption text area
        self.caption_text = ScrolledText(preview_frame, height=8, wrap=tk.WORD,
                                        font=('Segoe UI', 10))
        self.caption_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Bind text change event to update character count
        self.caption_text.bind('<KeyRelease>', lambda e: self._update_caption_stats())
        
        # Caption controls
        caption_controls = ttk.Frame(preview_frame)
        caption_controls.pack(fill=tk.X)
        
        ttk.Button(caption_controls, text="💾 Save Caption", command=self.save_caption,
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(caption_controls, text="📋 Copy to Clipboard", command=self.copy_caption,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        
        # Caption stats
        self.caption_stats_label = ttk.Label(caption_controls, text="Characters: 0", 
                                           style='Status.TLabel')
        self.caption_stats_label.pack(side=tk.RIGHT)

    def create_posting_tab(self):
        """Create posting controls tab."""
        # Posting controls
        controls_frame = ttk.LabelFrame(self.posting_frame, text="📤 Posting Controls", padding="10")
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Mode selection
        mode_frame = ttk.Frame(controls_frame)
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(mode_frame, text="Posting Mode:", style='Header.TLabel').pack(side=tk.LEFT)
        
        self.posting_mode_var = tk.StringVar(value="manual")
        ttk.Radiobutton(mode_frame, text="Manual", variable=self.posting_mode_var, 
                       value="manual").pack(side=tk.LEFT, padx=(20, 10))
        ttk.Radiobutton(mode_frame, text="Scheduled", variable=self.posting_mode_var, 
                       value="scheduled").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(mode_frame, text="Auto", variable=self.posting_mode_var, 
                       value="auto").pack(side=tk.LEFT, padx=(0, 10))
        
        # Posting options
        options_frame = ttk.Frame(controls_frame)
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.dry_run_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="🧪 Dry Run Mode", 
                       variable=self.dry_run_var).pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(options_frame, text="Delay (seconds):").pack(side=tk.LEFT)
        self.delay_var = tk.IntVar(value=5)
        delay_spin = ttk.Spinbox(options_frame, from_=1, to=3600, width=10,
                                textvariable=self.delay_var)
        delay_spin.pack(side=tk.LEFT, padx=(5, 0))
        
        # Action buttons
        action_frame = ttk.Frame(controls_frame)
        action_frame.pack(fill=tk.X)
        
        ttk.Button(action_frame, text="🚀 Post Selected", command=self.post_selected,
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="📝 Save as Drafts", command=self.save_as_drafts,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="⏰ Schedule Posts", command=self.schedule_posts_dialog,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="🌟 Post All Accounts", command=self.post_all_accounts,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="⏹️ Stop Posting", command=self.stop_posting,
                  style='Danger.TButton').pack(side=tk.LEFT, padx=(0, 5))
        
        # Scheduling options
        schedule_options_frame = ttk.Frame(controls_frame)
        schedule_options_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(schedule_options_frame, text="📅 Scheduling Options:", style='Header.TLabel').pack(side=tk.LEFT)
        
        schedule_options = [
            ("draft", "Save as Draft (Recommended)"),
            ("immediately", "Post Immediately"),
            ("schedule_delay", "Schedule for Later"),
            ("optimal_time", "Optimal Time")
        ]
        
        for value, text in schedule_options:
            ttk.Radiobutton(schedule_options_frame, text=text, variable=self.schedule_type_var, 
                           value=value).pack(side=tk.LEFT, padx=(20, 10))
        
        # Progress section
        progress_frame = ttk.LabelFrame(self.posting_frame, text="📊 Posting Progress", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # Progress labels
        progress_labels = ttk.Frame(progress_frame)
        progress_labels.pack(fill=tk.X)
        
        self.progress_label = ttk.Label(progress_labels, text="Ready", style='Status.TLabel')
        self.progress_label.pack(side=tk.LEFT)
        
        self.eta_label = ttk.Label(progress_labels, text="", style='Status.TLabel')
        self.eta_label.pack(side=tk.RIGHT)
        
        # Rate limiting info
        rate_frame = ttk.LabelFrame(self.posting_frame, text="⏱️ Rate Limiting", padding="10")
        rate_frame.pack(fill=tk.BOTH, expand=True)
        
        self.rate_info_text = ScrolledText(rate_frame, height=6, wrap=tk.WORD,
                                         font=('Consolas', 9))
        self.rate_info_text.pack(fill=tk.BOTH, expand=True)

    def create_logs_tab(self):
        """Create activity logs tab."""
        # Log controls
        log_controls = ttk.Frame(self.logs_frame)
        log_controls.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(log_controls, text="📋 Activity Logs", style='Header.TLabel').pack(side=tk.LEFT)
        
        ttk.Button(log_controls, text="🧹 Clear Logs", command=self.clear_logs,
                  style='Warning.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(log_controls, text="💾 Export Logs", command=self.export_logs,
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(log_controls, text="🔄 Refresh", command=self.refresh_logs,
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        
        # Log display
        self.log_text = ScrolledText(self.logs_frame, wrap=tk.WORD,
                                   font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_messages_tab(self):
        """Create send messages tab with modern UI."""
        # Header
        header_frame = ttk.Frame(self.messages_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(header_frame, text="💬 Send Messages", style='Header.TLabel').pack(side=tk.LEFT)
        ttk.Button(header_frame, text="📊 Message History", command=self.show_message_history,
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(header_frame, text="⚙️ Settings", command=self.show_message_settings,
                  style='Primary.TButton').pack(side=tk.RIGHT, padx=(5, 0))

        # Main container with two columns
        main_container = ttk.Frame(self.messages_frame)
        main_container.pack(fill=tk.BOTH, expand=True)

        # Left column - Input and controls
        left_column = ttk.Frame(main_container)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # Account Discovery Section
        discovery_frame = ttk.LabelFrame(left_column, text="🔍 Account Discovery", padding="10")
        discovery_frame.pack(fill=tk.X, pady=(0, 10))

        # Tag and username inputs
        input_frame = ttk.Frame(discovery_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))

        # Hashtag input
        tag_frame = ttk.Frame(input_frame)
        tag_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(tag_frame, text="Hashtag:").pack(side=tk.LEFT)
        self.hashtag_var = tk.StringVar()
        hashtag_entry = ttk.Entry(tag_frame, textvariable=self.hashtag_var, width=30)
        hashtag_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 5))
        ttk.Button(tag_frame, text="🔍 Search", command=self.search_hashtag).pack(side=tk.RIGHT)

        # Username input
        user_frame = ttk.Frame(input_frame)
        user_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(user_frame, text="@Username:").pack(side=tk.LEFT)
        self.username_var = tk.StringVar()
        username_entry = ttk.Entry(user_frame, textvariable=self.username_var, width=30)
        username_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 5))
        ttk.Button(user_frame, text="🔍 Search", command=self.search_username).pack(side=tk.RIGHT)

        # Bulk upload
        bulk_frame = ttk.Frame(discovery_frame)
        bulk_frame.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(bulk_frame, text="📁 Upload User List", command=self.bulk_upload_users,
                  style='Primary.TButton').pack(side=tk.LEFT)
        self.bulk_file_label = ttk.Label(bulk_frame, text="No file selected", foreground='gray')
        self.bulk_file_label.pack(side=tk.LEFT, padx=(10, 0))

        # Message Composition Section
        compose_frame = ttk.LabelFrame(left_column, text="✍️ Compose Message", padding="10")
        compose_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Message template selection
        template_frame = ttk.Frame(compose_frame)
        template_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(template_frame, text="Template:").pack(side=tk.LEFT)
        self.message_template_var = tk.StringVar(value="custom")
        template_combo = ttk.Combobox(template_frame, textvariable=self.message_template_var,
                                     values=["custom", "promotion", "collaboration", "follow_up", "greeting"],
                                     state="readonly", width=15)
        template_combo.pack(side=tk.LEFT, padx=(10, 5))
        template_combo.bind('<<ComboboxSelected>>', self.on_template_change)
        ttk.Button(template_frame, text="💾 Save Template", command=self.save_template).pack(side=tk.RIGHT)

        # Message text area
        text_frame = ttk.Frame(compose_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        ttk.Label(text_frame, text="Message:").pack(anchor=tk.W)
        self.message_text = ScrolledText(text_frame, wrap=tk.WORD, height=8)
        self.message_text.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # Personalization options
        personal_frame = ttk.Frame(compose_frame)
        personal_frame.pack(fill=tk.X, pady=(0, 10))
        self.personalize_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(personal_frame, text="Add personalization (@username)",
                       variable=self.personalize_var).pack(side=tk.LEFT)

        # Scheduling Section
        schedule_frame = ttk.LabelFrame(left_column, text="⏰ Scheduling", padding="10")
        schedule_frame.pack(fill=tk.X)

        # Schedule options
        schedule_options = ttk.Frame(schedule_frame)
        schedule_options.pack(fill=tk.X, pady=(0, 10))

        self.schedule_var = tk.StringVar(value="now")
        ttk.Radiobutton(schedule_options, text="Send Now", variable=self.schedule_var,
                       value="now").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(schedule_options, text="Schedule", variable=self.schedule_var,
                       value="schedule").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(schedule_options, text="Recurring", variable=self.schedule_var,
                       value="recurring").pack(side=tk.LEFT)

        # Date/Time pickers (initially hidden)
        self.schedule_picker_frame = ttk.Frame(schedule_frame)

        # Bind schedule option changes
        self.schedule_var.trace('w', self.on_schedule_option_change)

        # Right column - User selection and queue
        right_column = ttk.Frame(main_container)
        right_column.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # User Selection Section
        selection_frame = ttk.LabelFrame(right_column, text="👥 Select Recipients", padding="10")
        selection_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Selection controls
        controls_frame = ttk.Frame(selection_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(controls_frame, text="✅ Select All", command=self.select_all_users).pack(side=tk.LEFT)
        ttk.Button(controls_frame, text="❌ Select None", command=self.select_no_users).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(controls_frame, text="🔄 Clear List", command=self.clear_user_list).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(controls_frame, text="", width=10).pack(side=tk.LEFT, expand=True)
        self.user_count_label = ttk.Label(controls_frame, text="0 users found")
        self.user_count_label.pack(side=tk.RIGHT)

        # User list with checkboxes
        self.create_user_list(selection_frame)

        # Action buttons
        action_frame = ttk.Frame(right_column)
        action_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(action_frame, text="🚀 Send Messages", command=self.send_messages,
                  style='Success.TButton').pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(action_frame, text="📋 Preview Queue", command=self.preview_queue,
                  style='Primary.TButton').pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))

        # Progress section
        self.message_progress_frame = ttk.Frame(right_column)
        self.message_progress_frame.pack(fill=tk.X, pady=(10, 0))
        # Will be populated during operations

        # Initialize template dropdown
        self._update_template_dropdown()

    # Event handlers and methods will continue in the next part...
    
    def setup_logging(self):
        """Setup logging thread."""
        self.log_thread = threading.Thread(target=self.update_logs_thread, daemon=True)
        self.log_thread.start()

    def setup_system_tray(self):
        """Setup system tray icon."""
        if not PYSTRAY_AVAILABLE:
            return
        
        try:
            # Create a simple icon (you can replace with actual icon file)
            image = Image.new('RGB', (64, 64), color='blue')
            
            menu = Menu(
                MenuItem("Show", self.show_from_tray),
                MenuItem("Hide", self.hide_to_tray),
                Menu.SEPARATOR,
                MenuItem("Exit", self.quit_application)
            )
            
            self.tray_icon = pystray.Icon("autoposter", image, "Instagram Autoposter", menu)
        except Exception as e:
            self.log_message("warning", f"Could not setup system tray: {e}")

    def log_message(self, level: str, message: str):
        """Add message to log queue with optimized threading."""
        if self.is_destroyed:
            return

        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_msg = f"[{timestamp}] {message}"

        # Add to queue for background processing
        try:
            self.log_queue.put_nowait((level, formatted_msg))
        except queue.Full:
            # If queue is full, remove oldest message and try again
            try:
                self.log_queue.get_nowait()
                self.log_queue.put_nowait((level, formatted_msg))
            except queue.Empty:
                pass

    def update_logs_thread(self):
        """Optimized thread to update logs from queue."""
        log_buffer = []
        last_update = time.time()

        while not self.is_destroyed:
            try:
                # Try to get a message with timeout
                level, msg = self.log_queue.get(timeout=0.5)

                # Buffer messages for batch update
                log_buffer.append((level, msg))

                # Update GUI if buffer is full or enough time has passed
                current_time = time.time()
                if len(log_buffer) >= 10 or (current_time - last_update) > 1.0:
                    if log_buffer:
                        # Batch update the GUI
                        self.safe_update_gui(self._batch_update_log_display, log_buffer.copy())
                        log_buffer.clear()
                        last_update = current_time

            except queue.Empty:
                # If no messages, still update if we have buffered messages
                if log_buffer and (time.time() - last_update) > 2.0:
                    self.safe_update_gui(self._batch_update_log_display, log_buffer.copy())
                    log_buffer.clear()
                    last_update = time.time()
                continue
            except Exception as e:
                if not self.is_destroyed:
                    print(f"Log thread error: {e}")
                break

    def _batch_update_log_display(self, messages):
        """Batch update log display for better performance."""
        if self.is_destroyed:
            return

        try:
            # Insert all messages at once
            for level, message in messages:
                self.log_text.insert(tk.END, message + "\n")

            # Only scroll to end once after all messages
            self.log_text.see(tk.END)

            # Limit log size to prevent memory issues
            if int(self.log_text.index('end-1c').split('.')[0]) > 1000:
                self.log_text.delete('1.0', '100.0')

        except Exception as e:
            print(f"Batch log update error: {e}")

    # Account management methods
    def add_account_dialog(self):
        """Show add account dialog."""
        dialog = AccountDialog(self.root, self.account_manager)
        if dialog.result:
            self.refresh_account_list()
            self.log_message("success", f"Added account: {dialog.result['name']}")

    def refresh_account_list(self):
        """Refresh the accounts listbox."""
        self.accounts_listbox.delete(0, tk.END)
        
        for account_id, account_instance in self.account_manager.get_all_accounts().items():
            status = account_instance.get_status()
            
            # Format display text
            login_indicator = "🟢" if status['is_logged_in'] else "🔴"
            auto_indicator = "⚡" if status['auto_post'] else ""
            
            display_text = f"{login_indicator} {status['username']} {auto_indicator}"
            
            self.accounts_listbox.insert(tk.END, display_text)
        
        # Update system stats
        self.update_system_stats()

    def on_account_select(self, event):
        """Handle account selection with improved messaging initialization."""
        selection = self.accounts_listbox.curselection()
        if selection:
            accounts = list(self.account_manager.get_all_accounts().keys())
            if selection[0] < len(accounts):
                account_id = accounts[selection[0]]
                self.current_account = self.account_manager.get_account(account_id)
                self.account_manager.set_active_account(account_id)

                # Clean up previous messaging instance
                if self.messaging:
                    try:
                        self.messaging.stop_message_worker()
                    except Exception as e:
                        self.log_message("warning", f"Error stopping previous messaging worker: {e}")
                    self.messaging = None

                # Initialize messaging for this account if logged in
                if self.current_account and self.current_account.auth_manager.is_logged_in:
                    try:
                        # Verify the login is still valid
                        if self.current_account.auth_manager.verify_login():
                            self.messaging = InstagramMessaging(
                                self.current_account.auth_manager.client,
                                account_id
                            )
                            self.messaging.start_message_worker()
                            self.log_message("success", f"✅ Messaging initialized for account: {account_id}")
                        else:
                            self.log_message("warning", f"⚠️ Account {account_id} session expired, please re-login")
                    except Exception as e:
                        self.messaging = None
                        self.log_message("error", f"❌ Failed to initialize messaging for {account_id}: {e}")
                else:
                    self.messaging = None
                    if self.current_account:
                        self.log_message("info", f"📱 Account {account_id} selected (not logged in - messaging disabled)")
                    else:
                        self.log_message("error", f"❌ Failed to load account: {account_id}")

                self.update_account_display()
                self.log_message("info", f"🔄 Selected account: {account_id}")

    def auto_select_account(self):
        """Auto-select the active account."""
        active_account = self.account_manager.get_active_account()
        if active_account:
            self.current_account = active_account
            # Select in listbox
            accounts = list(self.account_manager.get_all_accounts().keys())
            try:
                index = accounts.index(active_account.account_id)
                self.accounts_listbox.selection_set(index)
                self.update_account_display()
            except ValueError:
                pass

    def update_account_display(self):
        """Update the display for the current account."""
        if not self.current_account:
            return
        
        # Update dashboard
        self.update_dashboard()
        # Update content list
        self.update_content_list()
        # Update posting controls
        self.update_posting_controls()

    def update_system_stats(self):
        """Update system statistics display."""
        system_status = self.account_manager.get_system_status()
        
        self.stats_labels["accounts"].config(text=str(system_status['total_accounts']))
        self.stats_labels["logged_in"].config(text=str(system_status['logged_in_accounts']))
        self.stats_labels["posting"].config(text=str(system_status['auto_post_accounts']))
        self.stats_labels["queue"].config(text=str(system_status['total_queue_items']))
        
        # Update header status
        status_text = f"📊 {system_status['total_accounts']} accounts • {system_status['logged_in_accounts']} logged in • {system_status['total_pending_items']} pending posts"
        self.system_status.config(text=status_text)

    # Implementation of main functionality methods

    def update_dashboard(self):
        """Update dashboard with account information and stats."""
        try:
            if not self.current_account:
                # Clear dashboard if no account selected
                for widget in self.account_info_frame.winfo_children():
                    widget.destroy()
                for widget in self.posting_stats_frame.winfo_children():
                    widget.destroy()
                return

            # Update account info
            for widget in self.account_info_frame.winfo_children():
                widget.destroy()

            info = self.current_account.get_status()

            ttk.Label(self.account_info_frame, text=f"Account: {info['username']}",
                     style='Header.TLabel').grid(row=0, column=0, sticky='w', pady=(0, 5))

            ttk.Label(self.account_info_frame, text=f"Status: {'🟢 Logged In' if info['is_logged_in'] else '🔴 Not Logged In'}",
                     style='Status.TLabel').grid(row=1, column=0, sticky='w', pady=(0, 5))

            ttk.Label(self.account_info_frame, text=f"Auto-post: {'⚡ Enabled' if info['auto_post'] else '📴 Disabled'}",
                     style='Status.TLabel').grid(row=2, column=0, sticky='w', pady=(0, 5))

            ttk.Label(self.account_info_frame, text=f"Queue: {info['queue_count']} pending",
                     style='Status.TLabel').grid(row=3, column=0, sticky='w')

            # Update posting stats
            for widget in self.posting_stats_frame.winfo_children():
                widget.destroy()

            if info['posting_stats']:
                stats = info['posting_stats']

                ttk.Label(self.posting_stats_frame, text="Posting Statistics",
                         style='Header.TLabel').grid(row=0, column=0, columnspan=2, pady=(0, 10))

                stat_items = [
                    ("Total Posted", str(stats.get('total', 0))),
                    ("Pending", str(stats.get('pending', 0))),
                    ("Failed", str(stats.get('failed', 0))),
                    ("Last Post", stats.get('last_post_time', 'Never')[:19] if stats.get('last_post_time') else 'Never')
                ]

                for i, (label, value) in enumerate(stat_items, 1):
                    ttk.Label(self.posting_stats_frame, text=f"{label}:").grid(row=i, column=0, sticky='w', pady=2)
                    ttk.Label(self.posting_stats_frame, text=value, style='Status.TLabel').grid(row=i, column=1, sticky='w', pady=2)

        except Exception as e:
            self.log_message("error", f"Failed to update dashboard: {e}")
    def update_content_list(self):
        """Update the content file list with optimized performance."""
        try:
            if not self.current_account:
                return

            # Check if we need to update (simple caching)
            cache_key = f"{self.current_account.account_id}_content"
            current_time = time.time()

            if (cache_key in self._last_update_time and
                current_time - self._last_update_time[cache_key] < 1.0):  # Update max once per second
                return

            self._last_update_time[cache_key] = current_time

            # Clear existing items efficiently
            self._clear_tree_efficiently()
            self.item_to_index.clear()

            # Get queue items
            queue_items = self.current_account.queue_manager.get_queue_preview()

            if not queue_items:
                self._update_selection_status()
                return

            # Batch process items for better performance
            items_to_insert = []
            total_items = len(queue_items)

            for i, item in enumerate(queue_items):
                # Format file size with caching
                file_path = Path(item['file'])
                size_mb = self._get_cached_file_size(file_path)

                # Get selection status
                selected = item.get('selected', False)
                checkbox_text = "☑" if selected else "☐"

                # Prepare item data
                item_data = (
                    checkbox_text,
                    file_path.name,
                    size_mb,
                    item.get('status', 'pending'),
                    self._truncate_caption(item.get('caption', ''))
                )

                items_to_insert.append((i, item_data, selected))

            # Batch insert items
            self._batch_insert_tree_items(items_to_insert)

            # Update selection status
            self._update_selection_status()

        except Exception as e:
            self.log_message("error", f"Failed to update content list: {e}")

    def _clear_tree_efficiently(self):
        """Clear tree items efficiently."""
        try:
            # Use delete all children instead of individual deletes
            children = self.files_tree.get_children()
            if children:
                # Delete all at once by deleting the root's children
                for item in children:
                    self.files_tree.delete(item)
        except Exception:
            # Fallback to individual deletion
            for item in self.files_tree.get_children():
                try:
                    self.files_tree.delete(item)
                except:
                    pass

    def _get_cached_file_size(self, file_path):
        """Get file size with caching to avoid repeated stat calls."""
        try:
            cache_key = f"size_{file_path}"
            if cache_key in self._file_list_cache:
                cached_time, cached_size = self._file_list_cache[cache_key]
                if time.time() - cached_time < 30:  # Cache for 30 seconds
                    return cached_size

            if file_path.exists():
                size_mb = f"{file_path.stat().st_size / (1024*1024):.1f} MB"
            else:
                size_mb = "N/A"

            self._file_list_cache[cache_key] = (time.time(), size_mb)
            return size_mb
        except:
            return "N/A"

    def _truncate_caption(self, caption):
        """Truncate caption efficiently."""
        if not caption:
            return ""
        return caption[:50] + "..." if len(caption) > 50 else caption

    def _batch_insert_tree_items(self, items_data):
        """Batch insert tree items for better performance."""
        try:
            # Disable updates during batch insert
            self.files_tree.configure(displaycolumns=())  # Temporarily hide

            for i, item_data, selected in items_data:
                item_id = self.files_tree.insert("", "end", values=item_data)
                self.item_to_index[item_id] = i

                # Apply tags
                tag = 'selected' if selected else 'unselected'
                self.files_tree.item(item_id, tags=(tag,))

            # Re-enable display
            self.files_tree.configure(displaycolumns=self.files_tree.cget('columns'))

        except Exception as e:
            # Fallback to individual inserts
            for i, item_data, selected in items_data:
                try:
                    item_id = self.files_tree.insert("", "end", values=item_data)
                    self.item_to_index[item_id] = i

                    tag = 'selected' if selected else 'unselected'
                    self.files_tree.item(item_id, tags=(tag,))
                except:
                    pass

    def update_posting_controls(self):
        """Update posting controls based on current account."""
        try:
            if not self.current_account:
                return

            # Update rate limit info
            if hasattr(self.current_account, 'posting_manager'):
                rate_info = self.current_account.posting_manager.get_rate_limit_info()

                rate_text = f"""Rate Limit Status:
Posts Today: {rate_info.get('posts_today', 0)}
Remaining: {rate_info.get('remaining', 50)}
Can Post: {'Yes' if rate_info.get('can_post', True) else 'No'}
Last Post: {rate_info.get('last_post_time', 'Never')[:19] if rate_info.get('last_post_time') else 'Never'}
Next Post: {rate_info.get('next_post_time', 'Now')[:19] if rate_info.get('next_post_time') else 'Now'}"""

                self.rate_info_text.delete(1.0, tk.END)
                self.rate_info_text.insert(tk.END, rate_text)

        except Exception as e:
            self.log_message("error", f"Failed to update posting controls: {e}")

    def browse_folder(self):
        """Browse for content folder."""
        try:
            folder_path = filedialog.askdirectory(title="Select Content Folder")
            if folder_path:
                self.folder_var.set(folder_path)
                self.log_message("info", f"Selected folder: {folder_path}")
        except Exception as e:
            self.log_message("error", f"Failed to browse folder: {e}")

    def scan_folder(self):
        """Scan selected folder for video files with background processing."""
        try:
            folder_path = self.folder_var.get()
            if not folder_path or not os.path.exists(folder_path):
                messagebox.showerror("Error", "Please select a valid folder first")
                return

            if not self.current_account:
                messagebox.showwarning("Warning", "Please select an account first")
                return

            # Show progress
            self.progress_label.config(text="Scanning folder...")
            self.progress_var.set(10)

            def scan_worker():
                """Background folder scanning."""
                try:
                    # Load videos into queue
                    result = self.current_account.queue_manager.load_queue(folder_path)
                    return result, folder_path
                except Exception as e:
                    raise e

            def scan_callback(result, error=None):
                """Callback for scan completion."""
                if error:
                    self.log_message("error", f"Failed to scan folder: {error}")
                    messagebox.showerror("Error", f"Failed to scan folder: {error}")
                    self.progress_var.set(0)
                    self.progress_label.config(text="Scan failed")
                    return

                # Update UI
                self.safe_update_gui(self._update_content_list_after_scan, result, folder_path)

            # Run scan in background
            self.run_in_background(scan_worker, scan_callback)

        except Exception as e:
            self.log_message("error", f"Failed to start folder scan: {e}")
            messagebox.showerror("Error", f"Failed to start folder scan: {e}")

    def _update_content_list_after_scan(self, result, folder_path):
        """Update content list after background scan."""
        try:
            self.update_content_list()
            self.progress_var.set(100)
            self.progress_label.config(text="Scan complete")
            self.log_message("success", f"Scanned folder: {folder_path}")
        except Exception as e:
            self.log_message("error", f"Failed to update content list: {e}")
            self.progress_var.set(0)
            self.progress_label.config(text="Update failed")

    def select_all_files(self):
        """Select all files in the queue."""
        try:
            if self.current_account:
                queue_len = len(self.current_account.queue_manager.queue)
                self.current_account.queue_manager.update_selection(list(range(queue_len)), True)
                self.update_content_list()
                selected_count = sum(1 for item in self.current_account.queue_manager.queue if item.get('selected', False))
                self._update_selection_status()
                self.log_message("success", f"Selected {selected_count} files")
        except Exception as e:
            self.log_message("error", f"Failed to select all files: {e}")

    def deselect_all_files(self):
        """Deselect all files in the queue."""
        try:
            if self.current_account:
                queue_len = len(self.current_account.queue_manager.queue)
                self.current_account.queue_manager.update_selection(list(range(queue_len)), False)
                self.update_content_list()
                # Clear caption preview when deselecting all
                self.selected_file_label.config(text="No file selected")
                self.caption_text.delete(1.0, tk.END)
                self._update_caption_stats()
                self._update_selection_status()
                self.log_message("success", "Deselected all files")
        except Exception as e:
            self.log_message("error", f"Failed to deselect all files: {e}")

    def invert_selection(self):
        """Invert the current selection (selected become unselected, unselected become selected)."""
        try:
            if not self.current_account:
                return

            # Invert selection for each item
            for i, item in enumerate(self.current_account.queue_manager.queue):
                current_selected = item.get('selected', False)
                item['selected'] = not current_selected

            # Save changes
            self.current_account.queue_manager.save_queue()

            # Update display
            self.update_content_list()
            self._update_selection_status()

            # Update selected count
            selected_count = sum(1 for item in self.current_account.queue_manager.queue if item.get('selected', False))
            self.log_message("success", f"Selection inverted. {selected_count} files now selected")

        except Exception as e:
            self.log_message("error", f"Failed to invert selection: {e}")

    def select_pending_files(self):
        """Select only files with 'pending' status."""
        try:
            if not self.current_account:
                return

            for i, item in enumerate(self.current_account.queue_manager.queue):
                is_pending = item.get('status', 'pending') == 'pending'
                item['selected'] = is_pending

            # Save changes
            self.current_account.queue_manager.save_queue()

            # Update display
            self.update_content_list()
            self._update_selection_status()

            # Update selected count
            selected_count = sum(1 for item in self.current_account.queue_manager.queue if item.get('selected', False))
            self.log_message("success", f"Selected {selected_count} pending files")

        except Exception as e:
            self.log_message("error", f"Failed to select pending files: {e}")

    def remove_selected_files(self):
        """Remove selected files from queue."""
        try:
            if not self.current_account:
                return

            # Get selected items (this is a simplified version)
            to_remove = []
            for i, item in enumerate(self.current_account.queue_manager.queue):
                if item.get('selected', False):
                    to_remove.append(i)

            # Remove in reverse order to maintain indices
            for i in reversed(to_remove):
                self.current_account.queue_manager.remove_item(i)

            self.update_content_list()
            self._update_selection_status()
            self.log_message("success", f"Removed {len(to_remove)} files from queue")

        except Exception as e:
            self.log_message("error", f"Failed to remove selected files: {e}")

    def edit_caption_dialog(self):
        """Open dialog to edit caption for selected file."""
        try:
            # Get selected item from tree
            selection = self.files_tree.selection()
            if not selection:
                messagebox.showwarning("Warning", "Please select a file first")
                return

            item = self.files_tree.item(selection[0])
            values = item['values']

            if len(values) < 5:
                return

            # Find the corresponding queue item
            filename = values[1]  # filename from tree
            if self.current_account:
                for i, queue_item in enumerate(self.current_account.queue_manager.queue):
                    if Path(queue_item['file']).name == filename:
                        self._show_caption_editor(i, queue_item)
                        break

        except Exception as e:
            self.log_message("error", f"Failed to edit caption: {e}")

    def _show_caption_editor(self, index, item):
        """Show caption editor dialog."""
        dialog = tk.Toplevel(self.root)
        dialog.title("Edit Caption")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Filename display
        ttk.Label(dialog, text=f"File: {Path(item['file']).name}",
                 style='Header.TLabel').pack(pady=10)

        # Caption text area
        text_frame = ttk.Frame(dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        caption_text = ScrolledText(text_frame, wrap=tk.WORD, height=10)
        caption_text.pack(fill=tk.BOTH, expand=True)
        caption_text.insert(tk.END, item.get('caption', ''))

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        def save_and_close():
            new_caption = caption_text.get(1.0, tk.END).strip()
            if self.current_account:
                self.current_account.queue_manager.edit_caption(index, new_caption)
                self.update_content_list()
                self.log_message("success", "Caption updated")
            dialog.destroy()

        ttk.Button(button_frame, text="💾 Save", command=save_and_close,
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cancel", command=dialog.destroy,
                  style='Danger.TButton').pack(side=tk.LEFT)

    def _on_tree_single_click(self, event):
        """Handle single clicks on treeview items."""
        try:
            # Identify what was clicked
            item_id = self.files_tree.identify_row(event.y)
            column = self.files_tree.identify_column(event.x)
            
            if item_id and column == "#1":  # Selected column (first column)
                self._toggle_item_selection(item_id)
                return "break"  # Prevent default selection
                
        except Exception as e:
            self.log_message("error", f"Failed to handle single click: {e}")
    
    def _on_tree_double_click(self, event):
        """Handle double clicks to edit captions."""
        try:
            item_id = self.files_tree.identify_row(event.y)
            if item_id:
                # Select the item first
                self.files_tree.selection_set(item_id)
                # Open caption edit dialog
                self.edit_caption_dialog()
                
        except Exception as e:
            self.log_message("error", f"Failed to handle double click: {e}")

    def _toggle_item_selection(self, item_id):
        """Toggle selection state of an item."""
        try:
            if not self.current_account:
                return

            # Get the queue index from our mapping
            if item_id in self.item_to_index:
                queue_index = self.item_to_index[item_id]
                
                if 0 <= queue_index < len(self.current_account.queue_manager.queue):
                    queue_item = self.current_account.queue_manager.queue[queue_index]
                    
                    # Toggle selection
                    current_selected = queue_item.get('selected', False)
                    new_selected = not current_selected

                    # Update queue item
                    self.current_account.queue_manager.queue[queue_index]['selected'] = new_selected
                    self.current_account.queue_manager.save_queue()

                    # Update display
                    self._update_item_display(item_id, new_selected)

                    # Update caption preview
                    filename = Path(queue_item['file']).name
                    if new_selected:
                        self._update_caption_preview(filename, queue_item.get('caption', ''))
                    elif self.selected_file_label.cget("text") == f"Selected: {filename}":
                        self.selected_file_label.config(text="No file selected")
                        self.caption_text.delete(1.0, tk.END)
                        self._update_caption_stats()

                    # Update selection status
                    self._update_selection_status()
                    
                    self.log_message("info", f"{'Selected' if new_selected else 'Deselected'}: {filename}")

        except Exception as e:
            self.log_message("error", f"Failed to toggle item selection: {e}")

    def _update_item_display(self, item_id, selected):
        """Update the display of an item to show selection state."""
        try:
            # Update checkbox text in selected column
            checkbox_text = "☑" if selected else "☐"
            self.files_tree.set(item_id, "selected", checkbox_text)

            # Update visual tags
            if selected:
                self.files_tree.item(item_id, tags=('selected',))
            else:
                self.files_tree.item(item_id, tags=('unselected',))

        except Exception as e:
            self.log_message("error", f"Failed to update item display: {e}")

    def _update_caption_preview(self, filename, caption):
        """Update caption preview for selected file."""
        try:
            self.selected_file_label.config(text=f"Selected: {filename}")
            self.caption_text.delete(1.0, tk.END)
            self.caption_text.insert(tk.END, caption)
            self._update_caption_stats()
        except Exception as e:
            self.log_message("error", f"Failed to update caption preview: {e}")

    def _on_file_select(self, event):
        """Handle file selection in treeview."""
        try:
            selection = self.files_tree.selection()
            if not selection:
                return

            item = self.files_tree.item(selection[0])
            values = item['values']

            if len(values) < 2:
                return

            filename = values[1]  # filename from tree

            # Find corresponding queue item and update caption preview
            if self.current_account:
                for queue_item in self.current_account.queue_manager.queue:
                    if Path(queue_item['file']).name == filename:
                        self._update_caption_preview(filename, queue_item.get('caption', ''))
                        break

        except Exception as e:
            self.log_message("error", f"Failed to handle file selection: {e}")

    def _update_caption_stats(self):
        """Update caption character count."""
        try:
            caption = self.caption_text.get(1.0, tk.END).strip()
            char_count = len(caption)
            self.caption_stats_label.config(text=f"Characters: {char_count}")
        except Exception as e:
            self.log_message("error", f"Failed to update caption stats: {e}")

    def _update_selection_status(self):
        """Update the selection status bar."""
        try:
            if not self.current_account:
                self.selection_status_label.config(text="No account selected")
                return

            total_files = len(self.current_account.queue_manager.queue)
            selected_files = sum(1 for item in self.current_account.queue_manager.queue if item.get('selected', False))
            pending_files = sum(1 for item in self.current_account.queue_manager.queue if item.get('status', 'pending') == 'pending')

            status_text = f"Total: {total_files} | Selected: {selected_files} | Pending: {pending_files}"

            if total_files > 0:
                selected_percentage = (selected_files / total_files) * 100
                status_text += f" | Selected: {selected_percentage:.1f}%"

            self.selection_status_label.config(text=status_text)

        except Exception as e:
            self.log_message("error", f"Failed to update selection status: {e}")
            self.selection_status_label.config(text="Error updating status")

    def _init_background_service(self):
        """Initialize background service."""
        try:
            from background_service import BackgroundService
            self.background_service = BackgroundService(self.account_manager, self.caption_generator)

            # Add status callback
            self.background_service.add_status_callback(self._background_status_callback)

            # Start background service
            if self.background_service.start_service():
                self.log_message("success", "Background service started")
            else:
                self.log_message("warning", "Failed to start background service")

        except Exception as e:
            self.log_message("error", f"Failed to initialize background service: {e}")

    def _background_status_callback(self, event_type, event_data, additional_data=None):
        """Handle background service status updates."""
        try:
            if event_type == "status":
                # Update service status in GUI
                self.root.after(0, self._update_service_status, event_data)
            elif event_type == "job":
                # Update job status
                self.root.after(0, self._update_job_status, event_data)
        except Exception as e:
            self.log_message("error", f"Background status callback error: {e}")

    def _update_service_status(self, status_data):
        """Update service status display."""
        try:
            # Update system status label
            uptime = status_data.get('uptime_formatted', 'Unknown')
            self.system_status.config(text=f"Background service active • Uptime: {uptime}")
        except Exception as e:
            self.log_message("error", f"Failed to update service status: {e}")

    def _update_job_status(self, job_data):
        """Update job status display."""
        try:
            job_id = job_data.get('job_id', 'Unknown')
            status = job_data.get('status', 'Unknown')
            account_id = job_data.get('account_id', 'Unknown')

            if status == "completed":
                self.log_message("success", f"Job {job_id} completed for account {account_id}")
            elif status == "failed":
                error = job_data.get('error_message', 'Unknown error')
                self.log_message("error", f"Job {job_id} failed for account {account_id}: {error}")
            elif status == "running":
                self.log_message("info", f"Job {job_id} running for account {account_id}")

            # Refresh content list to show updated status
            self.update_content_list()

        except Exception as e:
            self.log_message("error", f"Failed to update job status: {e}")

    def save_api_key(self):
        """Save OpenRouter API key to configuration."""
        try:
            api_key = self.api_key_var.get().strip()
            if not api_key:
                messagebox.showwarning("Warning", "Please enter an API key")
                return
            
            # Save to global config
            self.global_config.openrouter_api_key = api_key
            self.global_config.save_config()
            
            # Update caption generator
            self.caption_generator.api_key = api_key
            
            self.log_message("success", "OpenRouter API key saved successfully")
            messagebox.showinfo("Success", "API key saved successfully!")
            
        except Exception as e:
            self.log_message("error", f"Failed to save API key: {e}")
            messagebox.showerror("Error", f"Failed to save API key: {e}")
    
    def refresh_models(self):
        """Refresh available OpenRouter models."""
        try:
            self.log_message("info", "Refreshing OpenRouter models...")
            
            # Fetch fresh models
            models = self.caption_generator.fetch_available_models()
            
            if models:
                # Update comboboxes
                model_list = list(models.keys())
                
                # Find and update the comboboxes (need to get references)
                for widget in self.captions_frame.winfo_children():
                    if isinstance(widget, ttk.LabelFrame) and "Configuration" in widget.cget("text"):
                        for child in widget.winfo_children():
                            if isinstance(child, ttk.Frame):
                                for grandchild in child.winfo_children():
                                    if isinstance(grandchild, ttk.Combobox):
                                        grandchild['values'] = model_list
                
                self.log_message("success", f"Refreshed {len(models)} OpenRouter models")
                messagebox.showinfo("Success", f"Refreshed {len(models)} available models")
            else:
                self.log_message("warning", "No models found or API key not configured")
                messagebox.showwarning("Warning", "No models found. Check your API key.")
                
        except Exception as e:
            self.log_message("error", f"Failed to refresh models: {e}")
            messagebox.showerror("Error", f"Failed to refresh models: {e}")
    
    def save_caption_settings(self):
        """Save caption generation settings."""
        try:
            # Save model selections
            self.global_config.openrouter_default_model = self.openrouter_model_var.get()
            self.global_config.openrouter_fallback_model = self.fallback_model_var.get()
            
            # Save style and options
            self.global_config.caption_default_style = self.caption_style_var.get()
            self.global_config.auto_generate_captions = self.auto_generate_var.get()
            
            # Save configuration
            self.global_config.save_config()
            
            # Update caption generator config
            if self.caption_generator.config_manager:
                self.caption_generator.config = self.caption_generator._load_config_from_manager()
            
            self.log_message("success", "Caption settings saved successfully")
            messagebox.showinfo("Success", "Caption settings saved successfully!")
            
        except Exception as e:
            self.log_message("error", f"Failed to save settings: {e}")
            messagebox.showerror("Error", f"Failed to save settings: {e}")

    def test_api_connection(self):
        """Test OpenRouter API connection."""
        try:
            api_key = self.api_key_var.get()
            if not api_key:
                messagebox.showerror("Error", "Please enter an API key first")
                return

            # Temporarily set API key
            original_key = self.caption_generator.api_key
            self.caption_generator.api_key = api_key

            # Test connection
            result = self.caption_generator.test_api_connection()

            if result['success']:
                messagebox.showinfo("Success",
                    f"API connection successful!\n\nTest caption: {result.get('test_caption', 'N/A')}\nResponse time: {result.get('response_time', 0)}s")
                self.log_message("success", "API connection test successful")
            else:
                messagebox.showerror("Error", f"API test failed: {result.get('error', 'Unknown error')}")
                self.log_message("error", f"API test failed: {result.get('error', 'Unknown error')}")

            # Restore original key
            self.caption_generator.api_key = original_key

        except Exception as e:
            self.log_message("error", f"API test failed: {e}")
            messagebox.showerror("Error", f"API test failed: {e}")

    def generate_selected_captions(self):
        """Generate captions for selected files only."""
        try:
            if not self.current_account:
                messagebox.showwarning("Warning", "Please select an account first")
                return

            if not self.caption_generator.api_key:
                messagebox.showerror("Error", "Please configure API key first")
                return

            # Get selected items
            selected_items = [item for item in self.current_account.queue_manager.queue 
                            if item.get('selected', False)]

            if not selected_items:
                messagebox.showwarning("Warning", "No files selected")
                return

            style = self.caption_style_var.get()
            model = self.openrouter_model_var.get()

            # Progress callback
            def progress_callback(current, total, message):
                self.progress_var.set((current / total) * 100)
                self.progress_label.config(text=message)
                self.root.update()

            # Generate captions using bulk method
            result = self.caption_generator.generate_captions_for_queue_items(
                selected_items, style, model, progress_callback
            )

            if result['success']:
                # Save the queue with updated captions
                self.current_account.queue_manager.save_queue()
                self.update_content_list()
                
                self.progress_var.set(100)
                self.progress_label.config(text=f"Generated {result['generated_count']} captions")
                
                messagebox.showinfo("Success", 
                    f"Generated captions for {result['generated_count']}/{result['total_processed']} selected files")
                self.log_message("success", f"Generated {result['generated_count']} captions for selected files")
            else:
                messagebox.showerror("Error", f"Bulk generation failed: {result.get('error', 'Unknown error')}")

        except Exception as e:
            self.log_message("error", f"Failed to generate selected captions: {e}")
            messagebox.showerror("Error", f"Failed to generate captions: {e}")

    def generate_all_captions(self):
        """Generate captions for all files in queue using enhanced bulk generation."""
        try:
            if not self.current_account:
                messagebox.showwarning("Warning", "Please select an account first")
                return

            if not self.caption_generator.api_key:
                messagebox.showerror("Error", "Please configure API key first")
                return

            total_files = len(self.current_account.queue_manager.queue)

            if total_files == 0:
                messagebox.showinfo("Info", "No files in queue")
                return

            # Confirm with user
            if not messagebox.askyesno("Confirm", 
                f"Generate captions for all {total_files} files?\n\nThis may take several minutes and use API credits."):
                return

            style = self.caption_style_var.get()
            model = self.openrouter_model_var.get()

            # Progress callback
            def progress_callback(current, total, message):
                self.progress_var.set((current / total) * 100)
                self.progress_label.config(text=message)
                self.root.update()

            # Generate captions using bulk method
            result = self.caption_generator.generate_captions_for_queue_items(
                self.current_account.queue_manager.queue, style, model, progress_callback
            )

            if result['success']:
                # Save the queue with updated captions
                self.current_account.queue_manager.save_queue()
                self.update_content_list()
                
                self.progress_var.set(100)
                self.progress_label.config(text=f"Generated {result['generated_count']} captions")
                
                messagebox.showinfo("Success", 
                    f"Generated captions for {result['generated_count']}/{result['total_processed']} files")
                self.log_message("success", f"Bulk generation completed: {result['generated_count']} captions")
            else:
                messagebox.showerror("Error", f"Bulk generation failed: {result.get('error', 'Unknown error')}")

        except Exception as e:
            self.log_message("error", f"Failed to generate captions: {e}")
            messagebox.showerror("Error", f"Failed to generate captions: {e}")

    def clear_caption_cache(self):
        """Clear the caption cache."""
        try:
            self.caption_generator.clear_cache()
            messagebox.showinfo("Success", "Caption cache cleared")
            self.log_message("success", "Caption cache cleared")
        except Exception as e:
            self.log_message("error", f"Failed to clear cache: {e}")
            messagebox.showerror("Error", f"Failed to clear cache: {e}")

    def generate_single_caption(self):
        """Generate caption for selected file."""
        try:
            # Get selected file
            selection = self.files_tree.selection()
            if not selection:
                messagebox.showwarning("Warning", "Please select a file first")
                return

            item = self.files_tree.item(selection[0])
            values = item['values']

            if len(values) < 2:
                return

            filename = values[1]  # filename from tree
            style = self.caption_style_var.get()

            # Find corresponding queue item
            if self.current_account:
                for i, queue_item in enumerate(self.current_account.queue_manager.queue):
                    if Path(queue_item['file']).name == filename:
                        # Generate caption
                        result = self.caption_generator.generate_caption(queue_item['file'], style)
                        if result['success']:
                            self.current_account.queue_manager.edit_caption(i, result['caption'])
                            self.update_content_list()
                            self.caption_text.delete(1.0, tk.END)
                            self.caption_text.insert(tk.END, result['caption'])
                            self.selected_file_label.config(text=f"Selected: {filename}")
                            self.log_message("success", f"Generated caption for {filename}")
                        else:
                            messagebox.showerror("Error", f"Failed to generate caption: {result.get('error', 'Unknown error')}")
                        break

        except Exception as e:
            self.log_message("error", f"Failed to generate single caption: {e}")

    def save_caption(self):
        """Save the current caption."""
        try:
            caption = self.caption_text.get(1.0, tk.END).strip()
            if not caption:
                messagebox.showwarning("Warning", "Caption is empty")
                return

            filename = self.selected_file_label.cget("text").replace("Selected: ", "")
            if filename == "No file selected":
                messagebox.showwarning("Warning", "No file selected")
                return

            # Find and update queue item
            if self.current_account:
                for i, queue_item in enumerate(self.current_account.queue_manager.queue):
                    if Path(queue_item['file']).name == filename:
                        self.current_account.queue_manager.edit_caption(i, caption)
                        self.update_content_list()
                        self.log_message("success", f"Saved caption for {filename}")
                        break

        except Exception as e:
            self.log_message("error", f"Failed to save caption: {e}")
    def copy_caption(self):
        """Copy caption to clipboard."""
        try:
            caption = self.caption_text.get(1.0, tk.END).strip()
            if caption:
                self.root.clipboard_clear()
                self.root.clipboard_append(caption)
                self.log_message("success", "Caption copied to clipboard")
            else:
                messagebox.showwarning("Warning", "No caption to copy")
        except Exception as e:
            self.log_message("error", f"Failed to copy caption: {e}")

    def post_selected(self):
        """Post selected files with background processing."""
        try:
            if not self.current_account:
                messagebox.showwarning("Warning", "Please select an account first")
                return

            if not self.current_account.is_logged_in:
                messagebox.showwarning("Warning", "Please login to the account first")
                return

            # Get selected files
            selected_files = []
            for i, item in enumerate(self.current_account.queue_manager.queue):
                if item.get('selected', False):
                    selected_files.append((i, item))

            if not selected_files:
                messagebox.showwarning("Warning", "No files selected")
                return

            # Confirm posting
            if not messagebox.askyesno("Confirm", f"Post {len(selected_files)} selected files?"):
                return

            # Show progress
            self.progress_label.config(text=f"Posting {len(selected_files)} files...")
            self.progress_var.set(10)

            def posting_worker():
                """Background posting worker."""
                try:
                    posted_count = 0
                    failed_count = 0

                    for idx, (i, item) in enumerate(selected_files):
                        try:
                            # Update progress
                            progress = 10 + (idx / len(selected_files)) * 85
                            self.safe_update_gui(self._update_posting_progress,
                                               progress, f"Posting {idx+1}/{len(selected_files)}...")

                            # Post single item
                            success = self.current_account.posting_manager.post_single(
                                item,
                                dry_run=self.dry_run_var.get()
                            )

                            if success:
                                posted_count += 1
                                # Mark as posted in queue
                                item['status'] = 'posted'
                                item['posted_at'] = datetime.now().isoformat()
                            else:
                                failed_count += 1
                                item['status'] = 'failed'

                        except Exception as e:
                            failed_count += 1
                            self.safe_update_gui(self.log_message, "error", f"Failed to post {item['file']}: {e}")

                        # Small delay between posts
                        time.sleep(2)

                    # Save updated queue
                    self.current_account.queue_manager.save_queue()

                    return posted_count, failed_count, len(selected_files)

                except Exception as e:
                    raise e

            def posting_callback(result, error=None):
                """Callback for posting completion."""
                if error:
                    self.log_message("error", f"Posting failed: {error}")
                    self.safe_update_gui(self._finalize_posting, 0, 0, 0, True)
                    return

                posted, failed, total = result
                self.safe_update_gui(self._finalize_posting, posted, failed, total, False)

            # Run posting in background
            self.run_in_background(posting_worker, posting_callback)

        except Exception as e:
            self.log_message("error", f"Failed to start posting: {e}")
            self.safe_update_gui(self._finalize_posting, 0, 0, 0, True)

    def _update_posting_progress(self, progress, message):
        """Update posting progress."""
        if not self.is_destroyed:
            self.progress_var.set(progress)
            self.progress_label.config(text=message)

    def _finalize_posting(self, posted, failed, total, has_error):
        """Finalize posting operation."""
        if not self.is_destroyed:
            if has_error:
                self.progress_var.set(0)
                self.progress_label.config(text="Posting failed")
                messagebox.showerror("Error", "Posting operation failed")
            else:
                self.progress_var.set(100)
                self.progress_label.config(text=f"Complete! {posted}/{total} posted, {failed} failed")

                # Refresh content list to show updated status
                self.update_content_list()

                if posted > 0:
                    messagebox.showinfo("Success",
                                      f"Successfully posted {posted}/{total} files!\nFailed: {failed}")
                else:
                    messagebox.showwarning("Warning",
                                         f"No files were posted successfully.\nFailed: {failed}")

                self.log_message("success", f"Posting complete: {posted} posted, {failed} failed")

    def post_all_accounts(self):
        """Post from all active and logged-in accounts simultaneously."""
        try:
            # Get all active and logged-in accounts
            active_accounts = []
            for account_id, account in self.account_manager.get_all_accounts().items():
                if account.config.is_active and account.is_logged_in:
                    # Check if account has pending items
                    pending_items = [item for item in account.queue_manager.queue 
                                   if item.get('status') == 'pending']
                    if pending_items:
                        active_accounts.append(account_id)
            
            if not active_accounts:
                messagebox.showinfo("Info", "No active accounts with pending posts found.")
                return
            
            # Confirm with user
            account_names = [self.account_manager.get_account(acc_id).config.username 
                           for acc_id in active_accounts]
            message = f"Start posting from {len(active_accounts)} accounts?\n\n" + \
                     "\n".join([f"• {name}" for name in account_names])
            
            if not messagebox.askyesno("Confirm Multi-Account Posting", message):
                return
            
            self.log_message("info", f"Starting concurrent posting from {len(active_accounts)} accounts...")
            
            # Start concurrent posting in background thread
            def concurrent_posting():
                try:
                    results = self.account_manager.start_concurrent_posting(active_accounts)
                    
                    # Update GUI with results
                    self.root.after(0, self._update_posting_results, results)
                    
                except Exception as e:
                    self.root.after(0, self.log_message, "error", f"Concurrent posting failed: {e}")
            
            thread = threading.Thread(target=concurrent_posting, daemon=True)
            thread.start()
            
        except Exception as e:
            self.log_message("error", f"Failed to start multi-account posting: {e}")

    def _update_posting_results(self, results):
        """Update GUI with posting results."""
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        
        self.log_message("success", f"Multi-account posting completed: {successful}/{total} accounts posted successfully")
        
        # Log individual results
        for account_id, success in results.items():
            account = self.account_manager.get_account(account_id)
            username = account.config.username if account else account_id
            
            if success:
                self.log_message("success", f"✅ {username}: Posted successfully")
            else:
                self.log_message("error", f"❌ {username}: Posting failed")
        
        # Refresh displays
        self.refresh_account_list()
        self.update_content_list()

    def save_as_drafts(self):
        """Save selected videos as drafts for manual posting."""
        try:
            if not self.current_account:
                messagebox.showwarning("Warning", "Please select an account first")
                return

            # Get selected files
            selected_files = []
            for item in self.current_account.queue_manager.queue:
                if item.get('selected', False):
                    selected_files.append(item)

            if not selected_files:
                messagebox.showwarning("Warning", "No files selected")
                return

            # Confirm with user
            if not messagebox.askyesno("Confirm", 
                f"Save {len(selected_files)} videos as drafts?\n\nThey will be prepared for manual editing and posting in Instagram app."):
                return

            drafts_created = 0
            for item in selected_files:
                try:
                    result = self.schedule_manager.prepare_draft_post(
                        video_path=item['file'],
                        caption=item.get('caption', ''),
                        account_id=self.current_account.account_id
                    )
                    
                    if result['success']:
                        drafts_created += 1
                        item['status'] = 'draft_prepared'
                    else:
                        self.log_message("error", f"Failed to prepare draft for {item['file']}: {result['error']}")
                        
                except Exception as e:
                    self.log_message("error", f"Error preparing draft for {item['file']}: {e}")

            # Save changes
            self.current_account.queue_manager.save_queue()
            self.update_content_list()

            self.log_message("success", f"Prepared {drafts_created} drafts")
            messagebox.showinfo("Success", 
                f"Prepared {drafts_created}/{len(selected_files)} videos as drafts.\n\n" +
                "You can now edit captions and add music in the Instagram app before posting.")

        except Exception as e:
            self.log_message("error", f"Failed to save as drafts: {e}")
            messagebox.showerror("Error", f"Failed to save as drafts: {e}")

    def schedule_posts_dialog(self):
        """Open scheduling dialog for selected posts."""
        try:
            if not self.current_account:
                messagebox.showwarning("Warning", "Please select an account first")
                return

            # Get selected files
            selected_files = []
            for item in self.current_account.queue_manager.queue:
                if item.get('selected', False):
                    selected_files.append(item)

            if not selected_files:
                messagebox.showwarning("Warning", "No files selected")
                return

            # Create scheduling dialog
            schedule_dialog = SchedulingDialog(self.root, self.schedule_manager, 
                                             selected_files, self.current_account.account_id)
            
            if schedule_dialog.result:
                self.log_message("success", f"Scheduled {len(selected_files)} posts")
                self.update_content_list()

        except Exception as e:
            self.log_message("error", f"Failed to open scheduling dialog: {e}")
            messagebox.showerror("Error", f"Failed to schedule posts: {e}")

    def schedule_posts(self):
        """Schedule posts based on current settings."""
        try:
            if not self.current_account:
                messagebox.showwarning("Warning", "Please select an account first")
                return

            # Get selected files
            selected_files = []
            for item in self.current_account.queue_manager.queue:
                if item.get('selected', False):
                    selected_files.append(item)

            if not selected_files:
                messagebox.showwarning("Warning", "No files selected")
                return

            schedule_type = self.schedule_type_var.get()
            account_id = self.current_account.account_id

            # Schedule the videos
            result = self.schedule_manager.schedule_bulk_videos(
                video_items=selected_files,
                account_id=account_id,
                schedule_type=schedule_type
            )

            if result['success']:
                # Update status in queue
                for item in selected_files:
                    if schedule_type == 'draft':
                        item['status'] = 'draft_prepared'
                    else:
                        item['status'] = 'scheduled'

                self.current_account.queue_manager.save_queue()
                self.update_content_list()

                message = f"Scheduled {result['scheduled_count']}/{result['total_videos']} videos"
                if schedule_type == 'draft':
                    message += " as drafts for manual posting"
                elif schedule_type == 'immediately':
                    message += " for immediate posting"
                else:
                    message += f" for later posting"

                self.log_message("success", message)
                messagebox.showinfo("Success", message + "!")
            else:
                messagebox.showerror("Error", "Failed to schedule posts")

        except Exception as e:
            self.log_message("error", f"Failed to schedule posts: {e}")
            messagebox.showerror("Error", f"Failed to schedule posts: {e}")

    def stop_posting(self):
        """Stop current posting operation."""
        try:
            if self.current_account and hasattr(self.current_account, 'posting_manager'):
                self.current_account.posting_manager._posting_active = False
                self.log_message("success", "Posting stopped")
            else:
                messagebox.showwarning("Warning", "No active posting to stop")
        except Exception as e:
            self.log_message("error", f"Failed to stop posting: {e}")

    def clear_logs(self):
        """Clear activity logs."""
        try:
            self.log_text.delete(1.0, tk.END)
            self.log_message("success", "Logs cleared")
        except Exception as e:
            self.log_message("error", f"Failed to clear logs: {e}")
    def export_logs(self):
        """Export logs to file."""
        try:
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if file_path:
                with open(file_path, 'w') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log_message("success", f"Logs exported to {file_path}")
        except Exception as e:
            self.log_message("error", f"Failed to export logs: {e}")

    def refresh_logs(self):
        """Refresh logs display."""
        try:
            # Logs are updated in real-time, so just ensure display is current
            self.log_text.see(tk.END)
        except Exception as e:
            self.log_message("error", f"Failed to refresh logs: {e}")

    def login_account(self):
        """Login to selected account with enhanced error handling and progress tracking."""
        try:
            print("DEBUG: Login button clicked")  # Debug print
            if not self.current_account:
                print("DEBUG: No current account selected")  # Debug print
                messagebox.showwarning("Warning", "Please select an account first")
                return

            # Get credentials from config
            username = self.current_account.config.username
            password = self.current_account.config.password
            print(f"DEBUG: Username: {username}, Password: {'*' * len(password) if password else 'None'}")  # Debug print

            if not username or not password:
                print("DEBUG: Missing credentials")  # Debug print
                messagebox.showerror("Error", "Account credentials not configured")
                return

            # Show progress
            self.progress_label.config(text="Preparing login...")
            self.progress_var.set(10)
            self.root.update_idletasks()
            print("DEBUG: Starting login process...")  # Debug print

            # Check for IP blacklist before attempting login
            try:
                # Quick test to see if IP is blacklisted
                from instagrapi import Client
                test_client = Client()
                # This will fail quickly if IP is blacklisted
                print("DEBUG: Testing IP status...")
            except Exception as e:
                print(f"DEBUG: IP test error: {e}")

            # Check for existing session
            self.progress_label.config(text="Checking existing session...")
            self.progress_var.set(25)
            self.root.update_idletasks()
            
            # Try to load existing session first
            session_loaded = self.current_account.auth_manager.load_session()
            if session_loaded:
                self.log_message("info", f"Existing session found for {username}")
                self.progress_label.config(text="Verifying session...")
                self.progress_var.set(50)
                self.root.update_idletasks()
                
                # Verify the session is still valid
                if self.current_account.auth_manager.verify_login():
                    self.progress_var.set(100)
                    self.progress_label.config(text="Session restored successfully")
                    self.log_message("success", f"✅ Session restored for account: {username}")
                    self._on_login_success(username)
                    return
                else:
                    self.log_message("warning", f"Session expired for {username}, attempting fresh login")

            # Perform fresh login
            self.progress_label.config(text="Logging in...")
            self.progress_var.set(75)
            self.root.update_idletasks()

            # Use optimized background execution
            def login_worker():
                try:
                    success = self.current_account.login()
                    return success, username
                except Exception as e:
                    raise e

            def login_callback(result, error=None):
                success, username = result if result else (False, username)

                if error:
                    self.safe_update_gui(self._on_login_error, username, str(error))
                    return

                if success:
                    self.safe_update_gui(self._on_login_success, username)
                else:
                    self.safe_update_gui(self._on_login_failure, username)

            # Run login in background
            self.run_in_background(login_worker, login_callback)

        except Exception as e:
            self.log_message("error", f"Login preparation error: {e}")
            messagebox.showerror("Error", f"Login preparation failed: {e}")
            self.progress_var.set(0)
            self.progress_label.config(text="Login failed")
    
    def _on_login_success(self, username):
        """Handle successful login."""
        self.progress_var.set(100)
        self.progress_label.config(text="Login successful")
        self.log_message("success", f"✅ Logged in to account: {username}")
        
        # Initialize messaging for this account
        if self.current_account and self.current_account.auth_manager.is_logged_in:
            self.messaging = InstagramMessaging(
                self.current_account.auth_manager.client,
                self.current_account.account_id
            )
            self.messaging.start_message_worker()
            self.log_message("info", f"📱 Messaging initialized for account: {username}")
        
        # Update UI
        self.refresh_account_list()
        self.update_dashboard()
        messagebox.showinfo("Success", f"Successfully logged in as @{username}")
    
    def _on_login_failure(self, username):
        """Handle login failure."""
        self.progress_var.set(0)
        self.progress_label.config(text="Login failed")
        self.log_message("error", f"❌ Failed to login to account: {username}")
        
        # Get diagnostic information
        if self.current_account and self.current_account.auth_manager:
            diagnostics = self.current_account.auth_manager.diagnose_connection()
            error_msg = "Login failed. Possible issues:\n\n"
            
            if not diagnostics['session_file_valid']:
                error_msg += "• Session file is corrupted\n"
            if not diagnostics['api_accessible']:
                error_msg += "• Instagram API not accessible\n"
            if diagnostics['recommendations']:
                error_msg += f"• {chr(10).join(diagnostics['recommendations'])}\n"
                
            error_msg += "\nPlease check your credentials and try again."
        else:
            error_msg = "Login failed. Please check your credentials and try again."
            
        # Show error with action buttons for blacklist
        if "blacklist" in error_str or "ip address" in error_str:
            result = messagebox.askyesno("IP Blacklisted - Solutions Available",
                error_msg + "\n\nWould you like to see detailed solutions?")
            if result:
                self.show_blacklist_solutions()
        else:
            messagebox.showerror("Login Failed", error_msg)
    
    def _on_login_error(self, username, error):
        """Handle login error."""
        self.progress_var.set(0)
        self.progress_label.config(text="Login error")
        self.log_message("error", f"❌ Login error for {username}: {error}")

        error_str = str(error).lower()

        if "blacklist" in error_str or "ip address" in error_str:
            error_msg = f"🚫 IP BLACKLISTED\n\n"
            error_msg += f"Instagram has temporarily blacklisted your IP address.\n\n"
            error_msg += f"SOLUTIONS:\n"
            error_msg += f"1. ⏰ Wait 30-60 minutes before trying again\n"
            error_msg += f"2. 📱 Try using mobile hotspot or VPN\n"
            error_msg += f"3. 🌐 Login manually on Instagram website first\n"
            error_msg += f"4. 📧 Check for account verification emails\n\n"
            error_msg += f"This is temporary and will resolve automatically."
        elif "invalid" in error_str or "password" in error_str:
            error_msg = f"🔐 INVALID CREDENTIALS\n\n"
            error_msg += f"Username or password is incorrect.\n\n"
            error_msg += f"SOLUTIONS:\n"
            error_msg += f"1. ✅ Verify credentials on Instagram website\n"
            error_msg += f"2. 🔍 Check if account is locked/suspended\n"
            error_msg += f"3. 🔐 Disable 2FA temporarily if enabled"
        elif "challenge" in error_str:
            error_msg = f"🚫 CHALLENGE REQUIRED\n\n"
            error_msg += f"Instagram requires additional verification.\n\n"
            error_msg += f"SOLUTION:\n"
            error_msg += f"Login manually on Instagram website first\n"
            error_msg += f"to complete any required verifications."
        elif "2fa" in error_str or "two factor" in error_str:
            error_msg = f"🔐 2FA REQUIRED\n\n"
            error_msg += f"Two-factor authentication is required.\n\n"
            error_msg += f"SOLUTION:\n"
            error_msg += f"Disable 2FA temporarily or implement 2FA handling."
        elif "rate limit" in error_str:
            error_msg = f"⏰ RATE LIMITED\n\n"
            error_msg += f"Too many requests. Please wait before trying again."
        else:
            error_msg = f"❌ LOGIN FAILED\n\n{error}\n\n"
            error_msg += f"Please check your internet connection and credentials."
            
        # Show error with action buttons for blacklist
        if "blacklist" in error_str or "ip address" in error_str:
            result = messagebox.askyesno("IP Blacklisted - Solutions Available",
                error_msg + "\n\nWould you like to see detailed solutions?")
            if result:
                self.show_blacklist_solutions()
        else:
            messagebox.showerror("Login Error", error_msg)

    def show_blacklist_solutions(self):
        """Show detailed solutions for IP blacklist issue."""
        solutions_window = tk.Toplevel(self.root)
        solutions_window.title("Instagram IP Blacklist - Solutions")
        solutions_window.geometry("600x500")
        solutions_window.resizable(True, True)

        # Make it modal
        solutions_window.transient(self.root)
        solutions_window.grab_set()

        # Main frame with scrollbar
        main_frame = ttk.Frame(solutions_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = ttk.Label(main_frame, text="🚫 Instagram IP Blacklist Solutions",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # Scrollable text area
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, yscrollcommand=scrollbar.set,
                             font=('Arial', 10), bg='#f8f9fa', relief=tk.FLAT)
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=text_widget.yview)

        # Solutions content
        solutions_text = """🚨 PROBLEM: Instagram has temporarily blacklisted your IP address

This happens when there are too many login attempts from the same IP. It's temporary and will resolve automatically.

⏰ IMMEDIATE SOLUTIONS:

1. WAIT (RECOMMENDED)
   • Wait 30-60 minutes before trying again
   • This is the safest and most reliable solution
   • Instagram blacklists are usually temporary

2. CHANGE NETWORK
   • Use mobile hotspot instead of WiFi
   • Try from a different location
   • Use VPN to change IP address

3. MANUAL VERIFICATION
   • Go to instagram.com in your browser
   • Login manually with same credentials
   • Complete any verification steps
   • This often helps unblock the IP

4. CHECK ACCOUNT STATUS
   • Verify account isn't locked/suspended
   • Check email for verification requests
   • Ensure 2FA is disabled temporarily

🔧 TECHNICAL SOLUTIONS:

• Clear browser cookies for Instagram
• Restart your router/modem
• Try different device/browser
• Check if antivirus is blocking connections

⚠️ WHAT NOT TO DO:

• Don't keep trying repeatedly (makes it worse)
• Don't use multiple accounts from same IP
• Don't use automated tools while blacklisted

🎯 NEXT STEPS:

1. Close this application
2. Wait 30-60 minutes
3. Try logging into Instagram website manually
4. If successful, restart this application
5. If still failing, try mobile hotspot

💡 PREVENTION:

• Use session files to avoid repeated logins
• Add delays between login attempts
• Don't login too frequently
• Keep sessions active to avoid re-authentication

This is a common issue and will resolve automatically. Be patient! 🙂"""

        text_widget.insert(tk.END, solutions_text)
        text_widget.config(state=tk.DISABLED)

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="Open Instagram Website",
                  command=lambda: self.open_instagram_website()).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Close",
                  command=solutions_window.destroy).pack(side=tk.RIGHT)

    def open_instagram_website(self):
        """Open Instagram website for manual login."""
        import webbrowser
        webbrowser.open("https://www.instagram.com/accounts/login/")

    def refresh_current_session(self):
        """Manually refresh the current account's session."""
        try:
            if not self.current_account:
                messagebox.showwarning("Warning", "Please select an account first")
                return
            
            if not self.current_account.auth_manager.is_logged_in:
                messagebox.showwarning("Warning", "Account is not logged in")
                return
            
            # Show progress
            self.progress_label.config(text="Refreshing session...")
            self.progress_var.set(50)
            self.root.update_idletasks()
            
            # Refresh session in background thread
            def refresh_thread():
                try:
                    success = self.current_account.auth_manager.refresh_session()
                    
                    if success:
                        self.root.after(0, lambda: self._on_session_refresh_success())
                    else:
                        self.root.after(0, lambda: self._on_session_refresh_failure())
                        
                except Exception as e:
                    self.root.after(0, lambda: self._on_session_refresh_error(str(e)))
            
            threading.Thread(target=refresh_thread, daemon=True).start()
            
        except Exception as e:
            self.log_message("error", f"Session refresh preparation error: {e}")
            messagebox.showerror("Error", f"Session refresh failed: {e}")
            self.progress_var.set(0)
            self.progress_label.config(text="Refresh failed")
    
    def _on_session_refresh_success(self):
        """Handle successful session refresh."""
        self.progress_var.set(100)
        self.progress_label.config(text="Session refreshed")
        self.log_message("success", f"✅ Session refreshed for @{self.current_account.config.username}")
        messagebox.showinfo("Success", "Session refreshed successfully!")
        
        # Update account display
        self.refresh_account_list()
        self.update_dashboard()
    
    def _on_session_refresh_failure(self):
        """Handle session refresh failure."""
        self.progress_var.set(0)
        self.progress_label.config(text="Refresh failed")
        self.log_message("warning", f"⚠️ Session refresh failed for @{self.current_account.config.username}")
        messagebox.showwarning("Warning", "Session refresh failed. You may need to login again.")
    
    def _on_session_refresh_error(self, error):
        """Handle session refresh error."""
        self.progress_var.set(0)
        self.progress_label.config(text="Refresh error")
        self.log_message("error", f"❌ Session refresh error: {error}")
        messagebox.showerror("Error", f"Session refresh error: {error}")

    def verify_account(self):
        """Verify login status of selected account."""
        try:
            print("DEBUG: Verify button clicked")  # Debug print
            if not self.current_account:
                print("DEBUG: No current account selected for verification")  # Debug print
                messagebox.showwarning("Warning", "Please select an account first")
                return

            print(f"DEBUG: Verifying account: {self.current_account.config.username}")  # Debug print
            self.progress_label.config(text="Verifying...")
            self.progress_var.set(50)

            success = self.current_account.verify_login()
            print(f"DEBUG: Verification result: {success}")  # Debug print

            self.progress_var.set(100)
            if success:
                self.progress_label.config(text="Account verified")
                self.log_message("success", f"Account verified: {self.current_account.config.username}")
            else:
                self.progress_label.config(text="Verification failed")
                self.log_message("error", f"Account verification failed: {self.current_account.config.username}")

            self.refresh_account_list()
        except Exception as e:
            print(f"DEBUG: Verify account error: {e}")  # Debug print
            self.progress_label.config(text="Verification error")
            self.log_message("error", f"Verification error: {e}")
            messagebox.showerror("Error", f"Verification failed: {e}")
            self.update_dashboard()

        except Exception as e:
            self.log_message("error", f"Verification error: {e}")

    def remove_account(self):
        """Remove selected account."""
        try:
            if not self.current_account:
                messagebox.showwarning("Warning", "Please select an account first")
                return

            account_name = self.current_account.config.name

            if not messagebox.askyesno("Confirm", f"Remove account '{account_name}'? This cannot be undone."):
                return

            success = self.account_manager.remove_account(self.current_account.account_id)

            if success:
                self.log_message("success", f"Removed account: {account_name}")
                self.current_account = None
                self.refresh_account_list()
                self.auto_select_account()
            else:
                messagebox.showerror("Error", "Failed to remove account")

        except Exception as e:
            self.log_message("error", f"Failed to remove account: {e}")
    def refresh_all(self):
        """Refresh all data and displays."""
        try:
            self.refresh_account_list()
            self.update_dashboard()
            self.update_content_list()
            self.update_posting_controls()
            self.update_system_stats()
            self.log_message("success", "All data refreshed")
        except Exception as e:
            self.log_message("error", f"Failed to refresh all: {e}")

    def open_settings(self):
        """Open settings dialog."""
        try:
            # Create settings dialog
            dialog = tk.Toplevel(self.root)
            dialog.title("Settings")
            dialog.geometry("500x400")
            dialog.transient(self.root)
            dialog.grab_set()

            # Settings content would go here
            ttk.Label(dialog, text="Settings", style='Title.TLabel').pack(pady=20)
            ttk.Label(dialog, text="Settings dialog coming soon!", style='Header.TLabel').pack(pady=10)

            ttk.Button(dialog, text="Close", command=dialog.destroy).pack(pady=20)

        except Exception as e:
            self.log_message("error", f"Failed to open settings: {e}")

    # System tray methods
    def show_from_tray(self, icon=None, item=None):
        """Show window from system tray."""
        self.root.deiconify()
        self.root.lift()
        self.is_minimized_to_tray = False

    def hide_to_tray(self, icon=None, item=None):
        """Hide window to system tray."""
        self.root.withdraw()
        self.is_minimized_to_tray = True

    def quit_application(self, icon=None, item=None):
        """Quit the application with proper cleanup."""
        self.log_message("info", "🔄 Shutting down application...")
        
        # Stop session refresh timer
        self._stop_session_refresh_timer()
        
        # Stop system tray
        if self.tray_icon:
            self.tray_icon.stop()
            
        self.log_message("info", "👋 Application shutdown complete")
        self.root.quit()

    def on_closing(self):
        """Handle window closing event with optimized cleanup."""
        try:
            # Set destruction flag
            self.is_destroyed = True

            # Clear all pending debounced operations
            for timer_id in list(self.debounce_timers.values()):
                try:
                    self.root.after_cancel(timer_id)
                except:
                    pass
            self.debounce_timers.clear()

            # Stop GUI update processor
            self.safe_update_gui(lambda: None)  # Final empty update

            # Shutdown thread pool executor
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=False)

            # Save all active sessions before shutdown
            self._save_all_sessions()

            # Stop messaging worker gracefully
            if self.messaging:
                self.messaging.stop_message_worker()
                self.log_message("info", "📱 Messaging worker stopped")

            # Stop background service
            if self.background_service and self.background_service.is_running:
                self.background_service.stop_service()
                self.log_message("info", "⚙️ Background service stopped")

            # Save global configuration
            if hasattr(self, 'global_config'):
                self.global_config.save_config()
                self.log_message("info", "💾 Global configuration saved")

            # Clear caches to free memory
            self._file_list_cache.clear()
            self._user_list_cache.clear()
            self._last_update_time.clear()

            # Hide to tray or quit
            if PYSTRAY_AVAILABLE and self.tray_icon:
                self.hide_to_tray()
            else:
                self.quit_application()

        except Exception as e:
            print(f"Error during shutdown: {e}")
            self.quit_application()
    
    def _save_all_sessions(self):
        """Save sessions for all logged-in accounts to preserve login state."""
        try:
            saved_count = 0
            accounts = self.account_manager.get_all_accounts()
            
            for account_id, account in accounts.items():
                if account and account.auth_manager.is_logged_in:
                    try:
                        # Save session without logging out
                        success = account.auth_manager.save_session()
                        if success:
                            saved_count += 1
                            self.log_message("info", f"💾 Session saved for @{account.config.username}")
                        else:
                            self.log_message("warning", f"⚠️ Failed to save session for @{account.config.username}")
                    except Exception as e:
                        self.log_message("error", f"❌ Error saving session for {account_id}: {e}")
            
            if saved_count > 0:
                self.log_message("success", f"✅ Successfully saved {saved_count} account sessions")
            else:
                self.log_message("info", "ℹ️ No active sessions to save")
                
        except Exception as e:
            self.log_message("error", f"❌ Error saving sessions: {e}")
    
    def _auto_restore_sessions(self):
        """Automatically restore sessions for all configured accounts at startup."""
        def restore_thread():
            try:
                self.root.after(0, lambda: self.log_message("info", "🔄 Auto-restoring account sessions..."))
                
                accounts = self.account_manager.get_all_accounts()
                restored_count = 0
                total_accounts = len(accounts)
                
                if total_accounts == 0:
                    self.root.after(0, lambda: self.log_message("info", "ℹ️ No accounts configured for session restoration"))
                    return
                
                for i, (account_id, account) in enumerate(accounts.items()):
                    try:
                        progress = (i / total_accounts) * 100 if total_accounts > 0 else 0
                        self.root.after(0, lambda acc=account_id, p=progress: 
                                      self.log_message("info", f"🔄 Restoring session for {acc}... ({p:.0f}%)"))
                        
                        # Try to load and verify session
                        if account.auth_manager.load_session():
                            # Verify the session is actually working
                            if account.auth_manager.verify_login():
                                restored_count += 1
                                account.is_logged_in = True
                                self.root.after(0, lambda acc=account.config.username: 
                                              self.log_message("success", f"✅ Session restored for @{acc}"))
                                
                                # Initialize messaging for restored account if it's the current account
                                if self.current_account and self.current_account.account_id == account_id:
                                    self.root.after(0, lambda: self._initialize_messaging_for_current_account())
                            else:
                                self.root.after(0, lambda acc=account.config.username: 
                                              self.log_message("warning", f"⚠️ Session expired for @{acc}"))
                        else:
                            self.root.after(0, lambda acc=account.config.username: 
                                          self.log_message("info", f"ℹ️ No valid session found for @{acc}"))
                            
                        # Small delay between accounts to avoid overwhelming Instagram
                        time.sleep(1)
                        
                    except Exception as e:
                        self.root.after(0, lambda acc=account_id, err=str(e): 
                                      self.log_message("error", f"❌ Error restoring session for {acc}: {err}"))
                
                # Final summary
                if restored_count > 0:
                    self.root.after(0, lambda: self.log_message("success", 
                                                              f"🎉 Successfully restored {restored_count}/{total_accounts} account sessions"))
                    # Refresh the account list to show updated login statuses
                    self.root.after(0, lambda: self.refresh_account_list())
                else:
                    self.root.after(0, lambda: self.log_message("info", 
                                                              "ℹ️ No sessions were restored. You may need to login manually."))
                
            except Exception as e:
                self.root.after(0, lambda: self.log_message("error", f"❌ Error during session restoration: {e}"))
        
        # Start restoration in background thread
        threading.Thread(target=restore_thread, daemon=True).start()

    def _auto_login_check(self):
        """Check and perform auto-login if enabled and credentials are available."""
        def login_thread():
            try:
                self.root.after(0, lambda: self.log_message("info", "🔐 Checking for auto-login accounts..."))
                
                auto_login_attempts = 0
                successful_logins = 0
                
                accounts = self.account_manager.get_all_accounts()
                
                for account_id, account in accounts.items():
                    try:
                        # Check if auto-login conditions are met
                        config = account.config
                        auth = account.auth_manager
                        
                        # Check if auto-login is enabled and account is not logged in
                        auto_login_enabled = getattr(config, 'auto_login', True)  # Default to True
                        has_username = hasattr(config, 'username') and config.username
                        has_password = hasattr(config, 'password') and config.password
                        is_logged_in = auth.is_logged_in
                        
                        if auto_login_enabled and not is_logged_in and has_username and has_password:
                            auto_login_attempts += 1
                            self.root.after(0, lambda aid=account_id: 
                                          self.log_message("info", f"🔄 Attempting auto-login for {aid}..."))
                            
                            # Add delay to avoid rate limiting
                            time.sleep(1)
                            
                            # Attempt login with saved credentials
                            try:
                                success = auth.login(config.username, config.password)
                                
                                if success:
                                    successful_logins += 1
                                    self.root.after(0, lambda aid=account_id: 
                                                  self.log_message("success", f"✅ Auto-login successful for {aid}"))
                                    # Update UI to reflect login status
                                    self.root.after(0, self.update_account_dropdown)
                                else:
                                    self.root.after(0, lambda aid=account_id: 
                                                  self.log_message("warning", f"⚠️ Auto-login failed for {aid} - check credentials"))
                            except Exception as login_e:
                                self.root.after(0, lambda aid=account_id, err=str(login_e): 
                                              self.log_message("error", f"❌ Login error for {aid}: {err}"))
                                
                    except Exception as e:
                        self.root.after(0, lambda aid=account_id, err=str(e): 
                                      self.log_message("error", f"❌ Auto-login error for {aid}: {err}"))
                
                # Summary message
                if auto_login_attempts > 0:
                    self.root.after(0, lambda: 
                                  self.log_message("info", f"🔐 Auto-login complete: {successful_logins}/{auto_login_attempts} successful"))
                    
                    if successful_logins > 0:
                        self.root.after(0, self.update_account_status)
                else:
                    self.root.after(0, lambda: 
                                  self.log_message("info", "🔐 No accounts configured for auto-login"))
                        
            except Exception as e:
                self.root.after(0, lambda err=str(e): 
                              self.log_message("error", f"Error during auto-login check: {err}"))
        
        # Start the auto-login thread
        threading.Thread(target=login_thread, daemon=True).start()
    
    def _initialize_messaging_for_current_account(self):
        """Initialize messaging for the current account if logged in."""
        try:
            if self.current_account and self.current_account.auth_manager.is_logged_in:
                # Clean up previous messaging instance
                if self.messaging:
                    self.messaging.stop_message_worker()
                
                self.messaging = InstagramMessaging(
                    self.current_account.auth_manager.client,
                    self.current_account.account_id
                )
                self.messaging.start_message_worker()
                self.log_message("success", f"📱 Messaging initialized for @{self.current_account.config.username}")
        except Exception as e:
            self.log_message("error", f"❌ Failed to initialize messaging: {e}")
    
    def _start_session_refresh_timer(self):
        """Start periodic session refresh to keep accounts logged in."""
        def refresh_sessions():
            try:
                accounts = self.account_manager.get_all_accounts()
                refreshed_count = 0
                
                for account_id, account in accounts.items():
                    if account and account.auth_manager.is_logged_in:
                        try:
                            if account.auth_manager.refresh_session():
                                refreshed_count += 1
                        except Exception as e:
                            self.log_message("warning", f"⚠️ Session refresh failed for {account_id}: {e}")
                
                if refreshed_count > 0:
                    self.log_message("info", f"🔄 Refreshed {refreshed_count} account sessions")
                    
            except Exception as e:
                self.log_message("error", f"❌ Error during session refresh: {e}")
            
            # Schedule next refresh in 30 minutes
            self.session_refresh_timer = self.root.after(30 * 60 * 1000, refresh_sessions)  # 30 minutes
        
        # Start first refresh after 30 minutes
        self.session_refresh_timer = self.root.after(30 * 60 * 1000, refresh_sessions)
        self.log_message("info", "⏰ Session auto-refresh enabled (every 30 minutes)")
    
    def _stop_session_refresh_timer(self):
        """Stop the session refresh timer."""
        if self.session_refresh_timer:
            self.root.after_cancel(self.session_refresh_timer)
            self.session_refresh_timer = None
            self.log_message("info", "⏰ Session auto-refresh stopped")

    # Message functionality methods
    def create_user_list(self, parent):
        """Create user selection list with checkboxes."""
        # Treeview for user list
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Treeview with checkbox column
        columns = ("selected", "username", "fullname", "followers", "source")
        self.users_tree = ttk.Treeview(list_frame, columns=columns, show="headings",
                                      yscrollcommand=v_scrollbar.set,
                                      xscrollcommand=h_scrollbar.set, height=15)

        # Configure scrollbars
        v_scrollbar.config(command=self.users_tree.yview)
        h_scrollbar.config(command=self.users_tree.xview)

        # Column headers
        self.users_tree.heading("selected", text="✓", anchor='center')
        self.users_tree.heading("username", text="Username")
        self.users_tree.heading("fullname", text="Full Name")
        self.users_tree.heading("followers", text="Followers")
        self.users_tree.heading("source", text="Source")

        # Column widths
        self.users_tree.column("selected", width=50, minwidth=50, anchor='center')
        self.users_tree.column("username", width=120, minwidth=100)
        self.users_tree.column("fullname", width=150, minwidth=120)
        self.users_tree.column("followers", width=80, minwidth=70, anchor='center')
        self.users_tree.column("source", width=80, minwidth=70, anchor='center')

        self.users_tree.pack(fill=tk.BOTH, expand=True)

        # Bind events
        self.users_tree.bind('<Button-1>', self._on_user_click)
        self.users_tree.bind('<Double-1>', self._on_user_double_click)

        # Initialize user data
        self.discovered_users = {}  # username -> user_data

    def _on_user_click(self, event):
        """Handle user list click events."""
        region = self.users_tree.identify_region(event.x, event.y)
        if region == "cell":
            column = self.users_tree.identify_column(event.x)
            if column == "#1":  # Selected column
                item = self.users_tree.identify_row(event.y)
                if item:
                    self.toggle_user_selection(item)

    def _on_user_double_click(self, event):
        """Handle user list double-click events."""
        item = self.users_tree.identify_row(event.y)
        if item:
            username = self.users_tree.item(item, "values")[1]
            # Open user profile or show details
            self.show_user_details(username)

    def toggle_user_selection(self, item):
        """Toggle selection state of a user."""
        values = list(self.users_tree.item(item, "values"))
        current_state = values[0]
        new_state = "✓" if current_state != "✓" else ""
        values[0] = new_state
        self.users_tree.item(item, values=values)

    def search_hashtag(self):
        """Search for users by hashtag with debouncing and optimized threading."""
        # Use debounced search to prevent excessive API calls
        self.debounced_search_hashtag()

    def _debounced_search_hashtag(self):
        """Debounced hashtag search implementation."""
        if not self.messaging:
            messagebox.showerror("Error", "No account selected or not logged in")
            return

        hashtag = self.hashtag_var.get().strip()
        if not hashtag:
            return  # Don't show warning for empty input during typing

        if not hashtag.startswith("#"):
            hashtag = "#" + hashtag

        # Show progress
        original_hashtag = hashtag
        self.hashtag_var.set(f"{hashtag} (searching...)")
        self.log_message("info", f"Searching for users with hashtag: {hashtag}")

        def search_worker():
            try:
                users = self.messaging.discover_users_by_hashtag(hashtag[1:], amount=20)
                return users, original_hashtag
            except Exception as e:
                raise e

        def search_callback(result, error=None):
            if error:
                self.log_message("error", f"Hashtag search failed: {error}")
                self.safe_update_gui(lambda: self.hashtag_var.set(original_hashtag))
                return

            users, hashtag = result
            self.safe_update_gui(self._update_user_list_from_search, users, hashtag)

        # Run search in background
        self.run_in_background(search_worker, search_callback)

    # Note: Debounced methods will be created in __init__ to avoid method binding issues

    def search_username(self):
        """Search for users by username with debouncing and optimized threading."""
        # Use debounced search to prevent excessive API calls
        self.debounced_search_username()

    def _debounced_search_username(self):
        """Debounced username search implementation."""
        if not self.messaging:
            messagebox.showerror("Error", "No account selected or not logged in")
            return

        username = self.username_var.get().strip()
        if not username:
            return  # Don't show warning for empty input during typing

        if username.startswith("@"):
            username = username[1:]

        # Show progress
        original_username = username
        self.username_var.set(f"@{username} (searching...)")
        self.log_message("info", f"Searching for user: @{username}")

        def search_worker():
            try:
                users = self.messaging.discover_users_by_username_search(username, amount=10)
                return users, original_username
            except Exception as e:
                raise e

        def search_callback(result, error=None):
            if error:
                self.log_message("error", f"Username search failed: {error}")
                self.safe_update_gui(lambda: self.username_var.set(original_username))
                return

            users, username = result
            self.safe_update_gui(self._update_user_list_from_search, users, f"@{username}")

        # Run search in background
        self.run_in_background(search_worker, search_callback)

    # Note: Debounced methods will be created in __init__ to avoid method binding issues

    def _update_user_list_from_search(self, users, source):
        """Update the user list with search results."""
        # Reset search input
        if "(searching...)" in self.hashtag_var.get():
            self.hashtag_var.set("")
        if "(searching...)" in self.username_var.get():
            self.username_var.set("")

        if not users:
            self.log_message("warning", f"No users found for search: {source}")
            return

        # Add users to list
        added_count = 0
        for user in users:
            try:
                user_data = {
                    'username': user.username,
                    'fullname': user.fullname or "N/A",
                    'user_id': user.user_id,  # Ensure user_id is captured
                    'followers': self._format_follower_count(user.follower_count),
                    'verified': "✓" if user.is_verified else "",
                    'private': "🔒" if user.is_private else "",
                    'source': source
                }
                self.add_user_to_list(user_data)
                added_count += 1
            except Exception as e:
                self.log_message("error", f"Error adding user @{user.username}: {e}")
        
        self.log_message("success", f"✅ Added {added_count} users from {source}")
        self.update_user_count()
    
    def _format_follower_count(self, count):
        """Format follower count for display."""
        if count is None:
            return "N/A"
        elif count >= 1000000:
            return f"{count / 1000000:.1f}M"
        elif count >= 1000:
            return f"{count / 1000:.1f}K"
        else:
            return str(count)

    def _mock_hashtag_search(self, hashtag):
        """Mock hashtag search for development."""
        # Simulate finding users
        mock_users = [
            {"username": "user1", "fullname": "User One", "followers": "10K", "source": hashtag},
            {"username": "user2", "fullname": "User Two", "followers": "5K", "source": hashtag},
            {"username": "user3", "fullname": "User Three", "followers": "25K", "source": hashtag},
        ]

        for user in mock_users:
            self.add_user_to_list(user)

        self.update_user_count()

    def _mock_username_search(self, username):
        """Mock username search for development."""
        # Simulate finding user
        mock_user = {"username": username, "fullname": f"{username.title()} Official",
                    "followers": "15K", "source": "@search"}

        self.add_user_to_list(mock_user)
        self.update_user_count()

    def add_user_to_list(self, user_data):
        """Add a user to the selection list."""
        username = user_data["username"]
        if username in self.discovered_users:
            return  # Already in list

        self.discovered_users[username] = user_data

        item_id = self.users_tree.insert("", tk.END, values=(
            "",  # selected
            username,
            user_data["fullname"],
            user_data["followers"],
            user_data["source"]
        ))

        return item_id

    def update_user_count(self):
        """Update the user count display."""
        count = len(self.discovered_users)
        self.user_count_label.config(text=f"{count} users found")

    def select_all_users(self):
        """Select all users in the list."""
        for item in self.users_tree.get_children():
            values = list(self.users_tree.item(item, "values"))
            values[0] = "✓"
            self.users_tree.item(item, values=values)

    def select_no_users(self):
        """Deselect all users in the list."""
        for item in self.users_tree.get_children():
            values = list(self.users_tree.item(item, "values"))
            values[0] = ""
            self.users_tree.item(item, values=values)

    def clear_user_list(self):
        """Clear all users from the list."""
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        self.discovered_users.clear()
        self.update_user_count()

    def bulk_upload_users(self):
        """Handle bulk upload of user list."""
        file_path = filedialog.askopenfilename(
            title="Select user list file",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            self.process_bulk_upload(file_path)

    def process_bulk_upload(self, file_path):
        """Process bulk uploaded user file with threaded user ID resolution."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse usernames (support multiple formats)
            usernames = []
            lines = content.split('\n')

            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):  # Skip comments and empty lines
                    continue

                # Remove @ if present
                if line.startswith('@'):
                    line = line[1:]

                # Handle CSV format
                if ',' in line:
                    parts = [part.strip() for part in line.split(',')]
                    usernames.extend([p for p in parts if p and not p.startswith('#')])
                else:
                    usernames.append(line)

            # Filter out empty usernames and limit for safety
            usernames = [u for u in usernames[:200] if u and len(u.strip()) > 0]  # Increased limit
            
            if not usernames:
                messagebox.showwarning("No Users", "No valid usernames found in the file.")
                return

            # Check if account is logged in
            if not self.messaging or not self.current_account or not self.current_account.auth_manager.is_logged_in:
                # Add users without resolution
                self.log_message("warning", "⚠️ Account not logged in - adding users without ID resolution")
                for username in usernames:
                    user_data = {
                        "username": username, 
                        "fullname": f"{username.title()} (Login Required)",
                        "followers": "N/A", 
                        "source": "upload",
                        "user_id": None
                    }
                    self.add_user_to_list(user_data)
                
                self.update_user_count()
                self.bulk_file_label.config(text=f"Loaded {len(usernames)} users (login required for resolution)")
                messagebox.showinfo("Upload Complete", f"Added {len(usernames)} users.\nLogin to account to resolve user IDs for messaging.")
                return

            # Start threaded resolution process
            self._start_threaded_resolution(usernames, file_path)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to process file: {e}")
            self.log_message("error", f"Bulk upload error: {e}")

    def _start_threaded_resolution(self, usernames, file_path):
        """Start threaded username resolution process."""
        # Create progress dialog
        progress_dialog = tk.Toplevel(self.root)
        progress_dialog.title("Resolving Usernames")
        progress_dialog.geometry("450x200")
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()
        progress_dialog.protocol("WM_DELETE_WINDOW", lambda: None)  # Prevent closing during process
        
        # Progress widgets
        main_frame = ttk.Frame(progress_dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        title_label = ttk.Label(main_frame, text="Resolving usernames to user IDs...", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))
        
        progress_bar = ttk.Progressbar(main_frame, mode='determinate', length=350)
        progress_bar.pack(pady=5)
        progress_bar['maximum'] = len(usernames)
        
        status_label = ttk.Label(main_frame, text="Starting...", wraplength=400)
        status_label.pack(pady=5)
        
        stats_label = ttk.Label(main_frame, text="Processed: 0/0 | Success: 0 | Failed: 0")
        stats_label.pack(pady=5)
        
        # Cancel button
        cancel_var = tk.BooleanVar()
        cancel_btn = ttk.Button(main_frame, text="Cancel", command=lambda: cancel_var.set(True))
        cancel_btn.pack(pady=10)
        
        # Thread communication
        result_queue = queue.Queue()
        
        # Counters
        self.resolution_stats = {
            'processed': 0,
            'success': 0,
            'failed': 0,
            'total': len(usernames)
        }
        
        # Start background thread
        def resolution_worker():
            try:
                for i, username in enumerate(usernames):
                    if cancel_var.get():
                        break
                        
                    try:
                        # Send status update
                        result_queue.put(('status', f"Resolving @{username}...", i))
                        
                        # Resolve user ID with robust error handling
                        try:
                            user_id = self._fast_resolve_user_id(username)
                        except Exception as resolve_error:
                            self.log_message("error", f"❌ Critical error resolving @{username}: {resolve_error}")
                            user_id = None
                        
                        if user_id:
                            user_data = {
                                "username": username,
                                "fullname": f"{username.title()}",
                                "followers": "N/A",
                                "source": "upload",
                                "user_id": user_id
                            }
                            result_queue.put(('success', user_data, f"✅ @{username} → ID: {user_id}"))
                        else:
                            user_data = {
                                "username": username,
                                "fullname": f"{username.title()} (Not Found)",
                                "followers": "N/A",
                                "source": "upload",
                                "user_id": None
                            }
                            result_queue.put(('failed', user_data, f"❌ Could not resolve @{username}"))
                            
                    except Exception as e:
                        user_data = {
                            "username": username,
                            "fullname": f"{username.title()} (Error)",
                            "followers": "N/A",
                            "source": "upload",
                            "user_id": None
                        }
                        result_queue.put(('error', user_data, f"❌ Error resolving @{username}: {str(e)[:50]}"))
                
                result_queue.put(('done', None, "Resolution complete"))
                
            except Exception as e:
                result_queue.put(('error', None, f"Worker error: {e}"))
        
        # Start worker thread
        worker_thread = threading.Thread(target=resolution_worker, daemon=True)
        worker_thread.start()
        
        # Process results from queue
        def process_queue():
            try:
                while True:
                    try:
                        msg_type, data, message = result_queue.get_nowait()
                        
                        if msg_type == 'status':
                            # Update progress
                            progress_bar['value'] = data
                            status_label.config(text=message)
                            
                        elif msg_type in ['success', 'failed', 'error']:
                            # Add user to list
                            if data:
                                self.add_user_to_list(data)
                            
                            # Update stats
                            self.resolution_stats['processed'] += 1
                            if msg_type == 'success':
                                self.resolution_stats['success'] += 1
                            else:
                                self.resolution_stats['failed'] += 1
                            
                            # Update progress
                            progress_bar['value'] = self.resolution_stats['processed']
                            stats_text = f"Processed: {self.resolution_stats['processed']}/{self.resolution_stats['total']} | Success: {self.resolution_stats['success']} | Failed: {self.resolution_stats['failed']}"
                            stats_label.config(text=stats_text)
                            
                            # Log message
                            log_level = "success" if msg_type == 'success' else "error" if msg_type == 'error' else "warning"
                            self.log_message(log_level, message)
                            
                        elif msg_type == 'done':
                            # Completion
                            progress_dialog.destroy()
                            self.update_user_count()
                            
                            # Update status
                            success_count = self.resolution_stats['success']
                            failed_count = self.resolution_stats['failed']
                            total_count = success_count + failed_count
                            
                            status_text = f"Loaded {total_count} users ({success_count} resolved, {failed_count} unresolved)"
                            self.bulk_file_label.config(text=status_text)
                            
                            # Show completion message
                            completion_msg = f"Username resolution complete!\n\n"
                            completion_msg += f"✅ Successfully resolved: {success_count} users\n"
                            completion_msg += f"❌ Failed to resolve: {failed_count} users\n\n"
                            if failed_count > 0:
                                completion_msg += "Failed users can still be added but messaging may not work.\n"
                                completion_msg += "Check usernames are correct and accounts exist."
                            
                            messagebox.showinfo("Resolution Complete", completion_msg)
                            self.log_message("info", f"Resolution complete: {success_count} success, {failed_count} failed from {file_path}")
                            return
                            
                    except queue.Empty:
                        break
                        
            except Exception as e:
                self.log_message("error", f"Queue processing error: {e}")
            
            # Schedule next check
            if not cancel_var.get():
                self.root.after(100, process_queue)
            else:
                progress_dialog.destroy()
                self.log_message("info", "Username resolution cancelled by user")
        
        # Start queue processing
        self.root.after(100, process_queue)
    
    def _fast_resolve_user_id(self, username):
        """Fast and robust username to user ID resolution with multiple fallback methods."""
        try:
            if not self.messaging:
                self.log_message("error", "❌ No messaging service available")
                return None
                
            if not self.current_account or not self.current_account.auth_manager.is_logged_in:
                self.log_message("error", "❌ Account not logged in")
                return None
                
            # Use the messaging module's robust resolution method
            return self.messaging.resolve_user_id(username)
            
        except Exception as e:
            self.log_message("error", f"❌ Resolution error for @{username}: {e}")
            return None

    def on_template_change(self, event=None):
        """Handle template selection change."""
        template = self.message_template_var.get()

        # Built-in templates
        built_in_templates = {
            "custom": "",
            "promotion": "Hi! I loved your recent post about [topic]. Would you be interested in collaborating on similar content? Let's connect! 🚀",
            "collaboration": "Hello! I'm reaching out about potential collaboration opportunities. Your content style really resonates with our audience. Would love to discuss! 🤝",
            "follow_up": "Hi there! Following up on my previous message. I'd love to hear your thoughts on working together. Looking forward to your response! 📩",
            "greeting": "Hello! Hope you're having a great day. I came across your profile and think we might have some synergies. Would love to connect! 😊"
        }

        if template in built_in_templates:
            self.message_text.delete(1.0, tk.END)
            self.message_text.insert(1.0, built_in_templates[template])
        else:
            # Try to load from saved templates
            try:
                import json
                from pathlib import Path

                templates_file = Path("message_templates.json")
                if templates_file.exists():
                    with open(templates_file, 'r', encoding='utf-8') as f:
                        templates = json.load(f)

                    account_id = self.current_account.account_id if self.current_account else "global"
                    if template in templates:
                        template_data = templates[template]
                        if template_data.get("account_id") in [account_id, "global"]:
                            self.message_text.delete(1.0, tk.END)
                            self.message_text.insert(1.0, template_data["message"])
                            return

                # Template not found
                messagebox.showwarning("Template Not Found", f"Template '{template}' could not be loaded.")
                self.message_template_var.set("custom")

            except Exception as e:
                logger.error(f"Error loading template '{template}': {e}")
                messagebox.showerror("Error", f"Could not load template: {e}")
                self.message_template_var.set("custom")

    def save_template(self):
        """Save current message as a template."""
        message = self.message_text.get(1.0, tk.END).strip()
        if not message:
            messagebox.showwarning("Warning", "Please enter a message first")
            return

        # Create save template dialog
        save_dialog = tk.Toplevel(self.root)
        save_dialog.title("Save Template")
        save_dialog.geometry("400x200")

        ttk.Label(save_dialog, text="Save Message Template", font=('Arial', 12, 'bold')).pack(pady=10)

        # Template name input
        name_frame = ttk.Frame(save_dialog)
        name_frame.pack(fill=tk.X, padx=20, pady=(0, 10))
        ttk.Label(name_frame, text="Template Name:").pack(anchor=tk.W)
        name_var = tk.StringVar()
        name_entry = ttk.Entry(name_frame, textvariable=name_var, width=30)
        name_entry.pack(fill=tk.X, pady=(5, 0))
        name_entry.focus()

        # Category selection
        category_frame = ttk.Frame(save_dialog)
        category_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        ttk.Label(category_frame, text="Category:").pack(anchor=tk.W)
        category_var = tk.StringVar(value="custom")
        category_combo = ttk.Combobox(category_frame, textvariable=category_var,
                                     values=["custom", "promotion", "collaboration", "follow_up", "greeting"],
                                     state="readonly")
        category_combo.pack(fill=tk.X, pady=(5, 0))

        # Buttons
        button_frame = ttk.Frame(save_dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        def save_template_action():
            template_name = name_var.get().strip()
            if not template_name:
                messagebox.showerror("Error", "Please enter a template name")
                return

            self._save_template_to_file(template_name, message, category_var.get())
            messagebox.showinfo("Success", f"Template '{template_name}' saved successfully!")
            save_dialog.destroy()

        ttk.Button(button_frame, text="💾 Save", command=save_template_action,
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=save_dialog.destroy).pack(side=tk.RIGHT)

        # Bind Enter key
        save_dialog.bind('<Return>', lambda e: save_template_action())
        save_dialog.bind('<Escape>', lambda e: save_dialog.destroy())

    def _save_template_to_file(self, name, message, category):
        """Save a template to file."""
        try:
            import json
            from pathlib import Path

            templates_file = Path("message_templates.json")
            templates = {}

            # Load existing templates
            if templates_file.exists():
                with open(templates_file, 'r', encoding='utf-8') as f:
                    templates = json.load(f)

            # Add new template
            templates[name] = {
                "message": message,
                "category": category,
                "created_at": datetime.now().isoformat(),
                "account_id": self.current_account.account_id if self.current_account else "global"
            }

            # Save templates
            with open(templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates, f, indent=2, ensure_ascii=False)

            # Update template dropdown
            self._update_template_dropdown()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save template: {e}")

    def _update_template_dropdown(self):
        """Update template dropdown with saved templates."""
        try:
            import json
            from pathlib import Path

            templates_file = Path("message_templates.json")
            if not templates_file.exists():
                return

            with open(templates_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)

            # Filter templates by current account or global
            account_id = self.current_account.account_id if self.current_account else "global"
            available_templates = ["custom", "promotion", "collaboration", "follow_up", "greeting"]

            for name, data in templates.items():
                if data.get("account_id") in [account_id, "global"]:
                    if name not in available_templates:
                        available_templates.append(name)

            # Update combobox values
            if hasattr(self, 'message_template_var'):
                current_value = self.message_template_var.get()
                # Find the combobox widget and update its values
                for child in self.messages_frame.winfo_children():
                    if hasattr(child, 'winfo_children'):
                        for subchild in child.winfo_children():
                            if hasattr(subchild, 'winfo_children'):
                                for subsubchild in subchild.winfo_children():
                                    if hasattr(subsubchild, 'config') and hasattr(subsubchild, 'cget'):
                                        try:
                                            if subsubchild.cget('textvariable') == str(self.message_template_var):
                                                subsubchild.config(values=available_templates)
                                                break
                                        except:
                                            pass

        except Exception as e:
            logger.warning(f"Could not update template dropdown: {e}")

    def send_messages(self):
        """Send messages to selected users with live progress tracking."""
        if not self.messaging:
            messagebox.showerror("Error", "No account selected or not logged in")
            return

        selected_usernames = self.get_selected_users()
        if not selected_usernames:
            messagebox.showwarning("Warning", "Please select at least one user")
            return

        message = self.message_text.get(1.0, tk.END).strip()
        if not message:
            messagebox.showwarning("Warning", "Please enter a message")
            return

        # Get schedule type
        schedule_type_str = self.schedule_var.get()
        schedule_type = ScheduleType.NOW
        scheduled_time = None
        recurring_config = None

        if schedule_type_str == "schedule":
            # Parse scheduled time
            try:
                date_str = self.schedule_date_var.get()
                time_str = self.schedule_time_var.get()
                if date_str and time_str:
                    datetime_str = f"{date_str} {time_str}"
                    scheduled_time = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")
                    schedule_type = ScheduleType.SCHEDULED
                else:
                    messagebox.showerror("Error", "Please enter both date and time for scheduling")
                    return
            except ValueError as e:
                messagebox.showerror("Error", f"Invalid date/time format: {e}")
                return
        elif schedule_type_str == "recurring":
            # Parse recurring config
            try:
                duration_days = self.recurring_duration_var.get()
                frequency = self.recurring_freq_var.get()
                time_str = self.recurring_time_var.get()

                if not time_str:
                    messagebox.showerror("Error", "Please enter time for recurring messages")
                    return

                recurring_config = {
                    "frequency": frequency,
                    "time": time_str,
                    "duration_days": duration_days
                }
                schedule_type = ScheduleType.RECURRING
            except Exception as e:
                messagebox.showerror("Error", f"Invalid recurring config: {e}")
                return

        # Show progress dialog
        self.show_message_progress()
        self.update_message_progress(0, "Preparing user data...")
        
        # Get user objects for selected usernames
        selected_users = []
        users_needing_id_resolution = []

        for username in selected_usernames:
            if username in self.discovered_users:
                user_data = self.discovered_users[username]
                if not user_data.get('user_id'):  # Missing user ID
                    users_needing_id_resolution.append(user_data)
                else:
                    selected_users.append(user_data)

        # Resolve user IDs for users that need it
        if users_needing_id_resolution:
            self.update_message_progress(10, f"Resolving user IDs for {len(users_needing_id_resolution)} users...")
            self.log_message("info", f"Resolving user IDs for {len(users_needing_id_resolution)} users...")

            resolved_count = 0
            for i, user_data in enumerate(users_needing_id_resolution):
                progress = 10 + (i / len(users_needing_id_resolution)) * 20  # 10-30% for ID resolution
                self.update_message_progress(progress, f"Resolving @{user_data['username']}...")
                
                try:
                    user_id = self.messaging.resolve_user_id(user_data['username'])
                    if user_id:
                        user_data['user_id'] = user_id
                        selected_users.append(user_data)
                        resolved_count += 1
                        self.log_message("info", f"Resolved @{user_data['username']} -> ID: {user_id}")
                    else:
                        self.log_message("warning", f"Could not resolve user ID for @{user_data['username']}")
                except Exception as e:
                    self.log_message("error", f"Error resolving @{user_data['username']}: {e}")

            self.log_message("info", f"Resolved {resolved_count}/{len(users_needing_id_resolution)} user IDs")
            self.update_message_progress(30, f"Resolved {resolved_count}/{len(users_needing_id_resolution)} user IDs")

        if not selected_users:
            self.update_message_progress(0, "Error: No valid users found")
            messagebox.showerror("Error", "No valid users found (could not resolve user IDs)")
            return

        # Confirm sending
        schedule_desc = ""
        if schedule_type == ScheduleType.SCHEDULED:
            schedule_desc = f" at {scheduled_time.strftime('%Y-%m-%d %H:%M')}"
        elif schedule_type == ScheduleType.RECURRING:
            schedule_desc = f" recurring {recurring_config['frequency']} at {recurring_config['time']}"

        if not messagebox.askyesno("Confirm",
                                 f"Send message to {len(selected_users)} users{schedule_desc}?\n\nMessage: {message[:100]}..."):
            self.update_message_progress(0, "Cancelled by user")
            return

        # Add personalization if enabled
        final_message = message
        if self.personalize_var.get():
            # Note: Individual personalization will be handled in the messaging module
            pass

        # Start real-time messaging with progress tracking
        if schedule_type == ScheduleType.NOW:
            # Send immediately with live progress
            self._send_messages_with_progress(selected_users, final_message)
        else:
            # Queue for scheduled sending
            self._queue_scheduled_messages(selected_users, final_message, schedule_type, scheduled_time, recurring_config)

    def get_selected_users(self):
        """Get list of selected usernames."""
        selected = []
        for item in self.users_tree.get_children():
            values = self.users_tree.item(item, "values")
            if values[0] == "✓":  # Selected
                selected.append(values[1])  # Username
        return selected

    def _send_messages_with_progress(self, selected_users, message):
        """Send messages immediately with real-time progress tracking."""
        def send_thread():
            try:
                total_users = len(selected_users)
                sent_count = 0
                failed_count = 0
                
                self.root.after(0, lambda: self.update_message_progress(35, "Starting message sending..."))
                
                for i, user_data in enumerate(selected_users):
                    try:
                        # Update progress
                        progress = 35 + (i / total_users) * 60  # 35-95% for sending
                        status = f"Sending to @{user_data['username']} ({i+1}/{total_users})"
                        self.root.after(0, lambda p=progress, s=status: self.update_message_progress(p, s))
                        
                        # Add personalization if enabled
                        final_message = message
                        if hasattr(self, 'personalize_var') and self.personalize_var.get():
                            final_message = f"Hi @{user_data['username']}! {message}"
                        
                        # Check if user_id is available
                        user_id = user_data.get('user_id')
                        if not user_id:
                            # Try to resolve user_id if missing
                            username = user_data['username']
                            self.root.after(0, lambda u=username: 
                                          self.log_message("info", f"🔍 Resolving user ID for @{u}..."))
                            
                            # Try to resolve with timeout and better error handling
                            try:
                                user_id = self.messaging.resolve_user_id(username)
                                if user_id:
                                    user_data['user_id'] = user_id  # Cache for future use
                                    self.root.after(0, lambda u=username, uid=user_id: 
                                                  self.log_message("success", f"✅ Resolved @{u} → ID: {uid}"))
                                else:
                                    failed_count += 1
                                    self.root.after(0, lambda u=username: 
                                                  self.log_message("error", f"❌ Could not resolve user ID for @{u} - user may not exist or be private"))
                                    continue
                            except Exception as resolve_e:
                                failed_count += 1
                                self.root.after(0, lambda u=username, err=str(resolve_e)[:50]: 
                                              self.log_message("error", f"❌ Resolution failed for @{u}: {err}"))
                                continue
                        
                        # Send the message
                        success = self.messaging.send_direct_message(user_id, final_message)
                        
                        if success:
                            sent_count += 1
                            self.root.after(0, lambda u=user_data['username']: 
                                          self.log_message("success", f"✅ Message sent to @{u}"))
                        else:
                            failed_count += 1
                            self.root.after(0, lambda u=user_data['username']: 
                                          self.log_message("error", f"❌ Failed to send message to @{u}"))
                        
                        # Add delay between messages to respect rate limits (optimized)
                        time.sleep(0.3)  # Reduced delay for better performance
                        
                    except Exception as e:
                        failed_count += 1
                        self.root.after(0, lambda u=user_data['username'], err=str(e): 
                                      self.log_message("error", f"❌ Error sending to @{u}: {err}"))
                
                # Final update
                final_status = f"Complete! Sent: {sent_count}, Failed: {failed_count}"
                self.root.after(0, lambda: self.update_message_progress(100, final_status))
                self.root.after(0, lambda: self.log_message("info", f"📊 Messaging complete: {sent_count} sent, {failed_count} failed"))
                
                # Show completion dialog
                if sent_count > 0:
                    self.root.after(0, lambda: messagebox.showinfo("Complete", 
                                                                  f"Messages sent successfully!\n\nSent: {sent_count}\nFailed: {failed_count}"))
                else:
                    self.root.after(0, lambda: messagebox.showerror("Failed", 
                                                                   f"No messages were sent successfully.\nTotal failures: {failed_count}"))
                
            except Exception as e:
                self.root.after(0, lambda: self.log_message("error", f"Critical error in messaging thread: {e}"))
                self.root.after(0, lambda: self.update_message_progress(0, f"Error: {e}"))
        
        # Start sending in background thread with timeout protection
        def safe_send_thread():
            try:
                send_thread()
            except Exception as critical_e:
                self.root.after(0, lambda: self.log_message("error", f"💥 Critical messaging error: {critical_e}"))
                self.root.after(0, lambda: messagebox.showerror("Critical Error", 
                                                              f"Messaging failed with critical error:\n{critical_e}\n\nPlease check your login and try again."))
        
        threading.Thread(target=safe_send_thread, daemon=True).start()
    
    def _queue_scheduled_messages(self, selected_users, message, schedule_type, scheduled_time, recurring_config):
        """Queue messages for scheduled sending."""
        self.update_message_progress(35, "Queueing scheduled messages...")
        
        queued_count = 0
        failed_count = 0
        
        for i, user_data in enumerate(selected_users):
            try:
                progress = 35 + (i / len(selected_users)) * 60  # 35-95% for queueing
                self.update_message_progress(progress, f"Queueing for @{user_data['username']} ({i+1}/{len(selected_users)})")
                
                user = DiscoveredUser(
                    user_id=user_data['user_id'],
                    username=user_data['username'],
                    fullname=user_data['fullname'],
                    follower_count=int(user_data['followers'].replace('K', '000').replace('M', '000000')) if user_data['followers'].replace('K', '').replace('M', '').replace('N/A', '').isdigit() else None,
                    source=user_data['source']
                )

                task_id = self.messaging.add_message_task(
                    user=user,
                    message=message,
                    schedule_type=schedule_type,
                    scheduled_time=scheduled_time,
                    recurring_config=recurring_config
                )
                queued_count += 1
                self.log_message("info", f"📅 Queued message for @{user.username} (Task: {task_id})")
                
            except Exception as e:
                failed_count += 1
                self.log_message("error", f"❌ Failed to queue message for @{user_data['username']}: {e}")

        # Final update
        final_status = f"Queued: {queued_count}, Failed: {failed_count}"
        self.update_message_progress(100, final_status)
        
        if queued_count > 0:
            schedule_desc = ""
            if schedule_type == ScheduleType.SCHEDULED:
                schedule_desc = f" for {scheduled_time.strftime('%Y-%m-%d %H:%M')}"
            elif schedule_type == ScheduleType.RECURRING:
                schedule_desc = f" as {recurring_config['frequency']} recurring"
                
            self.log_message("success", f"📊 Successfully queued {queued_count} messages{schedule_desc}")
            messagebox.showinfo("Queued", f"Successfully queued {queued_count} messages{schedule_desc}")
        else:
            messagebox.showerror("Error", "Failed to queue any messages")

    def _mock_send_messages(self, users, message):
        """Mock message sending for development - DEPRECATED."""
        # This method is kept for backward compatibility but should not be used
        self.log_message("warning", "Using deprecated mock messaging - switching to real messaging")
        self._send_messages_with_progress(users, message)

    def preview_queue(self):
        """Preview message queue before sending."""
        if not self.messaging:
            messagebox.showerror("Error", "No account selected or not logged in")
            return

        selected_users = self.get_selected_users()
        message = self.message_text.get(1.0, tk.END).strip()

        if not selected_users:
            messagebox.showwarning("Warning", "No users selected")
            return

        if not message:
            messagebox.showwarning("Warning", "No message entered")
            return

        # Create preview dialog
        preview_dialog = tk.Toplevel(self.root)
        preview_dialog.title("Message Preview")
        preview_dialog.geometry("600x500")

        ttk.Label(preview_dialog, text="Message Queue Preview", font=('Arial', 12, 'bold')).pack(pady=10)

        # Preview frame
        preview_frame = ttk.Frame(preview_dialog)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # Message preview
        msg_frame = ttk.LabelFrame(preview_frame, text="Message Content", padding=10)
        msg_frame.pack(fill=tk.X, pady=(0, 10))

        msg_text = tk.Text(msg_frame, wrap=tk.WORD, height=6, font=('Arial', 10))
        msg_text.insert(1.0, message)
        msg_text.config(state=tk.DISABLED)
        msg_text.pack(fill=tk.X)

        # Scheduling info
        schedule_info = self._get_schedule_info_text()
        if schedule_info:
            schedule_frame = ttk.LabelFrame(preview_frame, text="Scheduling", padding=10)
            schedule_frame.pack(fill=tk.X, pady=(0, 10))
            ttk.Label(schedule_frame, text=schedule_info, font=('Arial', 9)).pack(anchor=tk.W)

        # Rate limit status
        rate_status = self.messaging.get_rate_limit_status()
        rate_frame = ttk.LabelFrame(preview_frame, text="Rate Limiting Status", padding=10)
        rate_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(rate_frame, text=f"Hourly: {rate_status['hourly_used']}/{rate_status['hourly_limit']} requests", font=('Arial', 9)).pack(anchor=tk.W)
        ttk.Label(rate_frame, text=f"Minutely: {rate_status['minutely_used']}/{rate_status['minutely_limit']} requests", font=('Arial', 9)).pack(anchor=tk.W)
        ttk.Label(rate_frame, text=f"Can send now: {'Yes' if rate_status['can_make_request'] else 'No'}", font=('Arial', 9)).pack(anchor=tk.W)

        # Recipients list
        recipients_frame = ttk.LabelFrame(preview_frame, text=f"Recipients ({len(selected_users)})", padding=10)
        recipients_frame.pack(fill=tk.BOTH, expand=True)

        recipients_text = tk.Text(recipients_frame, wrap=tk.WORD, font=('Arial', 9))
        recipients_text.insert(1.0, "\n".join([f"@{user}" for user in selected_users]))
        recipients_text.config(state=tk.DISABLED)

        scrollbar = ttk.Scrollbar(recipients_frame, command=recipients_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        recipients_text.config(yscrollcommand=scrollbar.set)
        recipients_text.pack(fill=tk.BOTH, expand=True)

        # Buttons
        button_frame = ttk.Frame(preview_dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        ttk.Button(button_frame, text="Send Now", command=lambda: self._send_from_preview(selected_users, message, preview_dialog),
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Close", command=preview_dialog.destroy).pack(side=tk.RIGHT)

    def _get_schedule_info_text(self):
        """Get schedule information as text."""
        schedule_type = self.schedule_var.get()
        if schedule_type == "now":
            return "Send immediately"
        elif schedule_type == "schedule":
            date_str = self.schedule_date_var.get()
            time_str = self.schedule_time_var.get()
            if date_str and time_str:
                return f"Scheduled for {date_str} at {time_str}"
            else:
                return "Scheduled (date/time not set)"
        elif schedule_type == "recurring":
            freq = self.recurring_freq_var.get()
            time_str = self.recurring_time_var.get()
            duration = self.recurring_duration_var.get()
            if time_str:
                return f"Recurring {freq} at {time_str} for {duration} days"
            else:
                return f"Recurring {freq} (time not set)"
        return ""

    def _send_from_preview(self, users, message, dialog):
        """Send messages from preview dialog."""
        dialog.destroy()

        # Create tasks for immediate sending
        queued_count = 0
        for username in users:
            if username in self.discovered_users:
                user_data = self.discovered_users[username]
                user = DiscoveredUser(
                    user_id=user_data.get('user_id', ''),
                    username=user_data['username'],
                    fullname=user_data['fullname'],
                    follower_count=int(user_data['followers'].replace('K', '000').replace('M', '000000')) if user_data['followers'].replace('K', '').replace('M', '').replace('N/A', '').isdigit() else None,
                    source=user_data['source']
                )

                try:
                    task_id = self.messaging.add_message_task(
                        user=user,
                        message=message,
                        schedule_type=ScheduleType.NOW
                    )
                    queued_count += 1
                except Exception as e:
                    self.log_message("error", f"Failed to queue message for @{username}: {e}")

        if queued_count > 0:
            self.log_message("info", f"Queued {queued_count} messages for immediate sending")
            messagebox.showinfo("Success", f"Queued {queued_count} messages for sending")
        else:
            messagebox.showerror("Error", "Failed to queue any messages")

    def show_message_progress(self):
        """Show message sending progress."""
        # Clear previous progress
        for widget in self.message_progress_frame.winfo_children():
            widget.destroy()

        # Add progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.message_progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))

        self.progress_label = ttk.Label(self.message_progress_frame, text="Preparing to send...")
        self.progress_label.pack()

    def update_message_progress(self, progress, status):
        """Update message sending progress."""
        self.progress_var.set(progress)
        self.progress_label.config(text=status)
        self.root.update_idletasks()

    def show_user_details(self, username):
        """Show detailed information about a user."""
        if username not in self.discovered_users:
            return

        user_data = self.discovered_users[username]

        # Create details dialog
        details_dialog = tk.Toplevel(self.root)
        details_dialog.title(f"User Details - @{username}")
        details_dialog.geometry("400x300")

        # User info
        info_frame = ttk.Frame(details_dialog, padding=20)
        info_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(info_frame, text=f"@{username}", font=('Arial', 14, 'bold')).pack(anchor=tk.W, pady=(0, 10))
        ttk.Label(info_frame, text=f"Full Name: {user_data['fullname']}").pack(anchor=tk.W, pady=(0, 5))
        ttk.Label(info_frame, text=f"Followers: {user_data['followers']}").pack(anchor=tk.W, pady=(0, 5))
        ttk.Label(info_frame, text=f"Source: {user_data['source']}").pack(anchor=tk.W, pady=(0, 5))

        # Action buttons
        button_frame = ttk.Frame(details_dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        ttk.Button(button_frame, text="Send Direct Message",
                  command=lambda: self.send_direct_to_user(username, details_dialog)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Close", command=details_dialog.destroy).pack(side=tk.RIGHT)

    def send_direct_to_user(self, username, dialog):
        """Send direct message to specific user."""
        dialog.destroy()
        # Set user as selected and trigger send
        self.select_user_only(username)
        self.send_messages()

    def select_user_only(self, username):
        """Select only the specified user."""
        self.select_no_users()  # Deselect all first

        for item in self.users_tree.get_children():
            values = list(self.users_tree.item(item, "values"))
            if values[1] == username:  # Username column
                values[0] = "✓"
                self.users_tree.item(item, values=values)
                break

    def show_message_history(self):
        """Show message history dialog."""
        history_dialog = tk.Toplevel(self.root)
        history_dialog.title("Message History")
        history_dialog.geometry("800x600")

        # Header
        header_frame = ttk.Frame(history_dialog)
        header_frame.pack(fill=tk.X, pady=(10, 5))

        ttk.Label(header_frame, text="📊 Message History", font=('Arial', 14, 'bold')).pack(side=tk.LEFT)
        ttk.Button(header_frame, text="🔄 Refresh", command=lambda: self._refresh_message_history(history_dialog)).pack(side=tk.RIGHT)

        # Filters
        filter_frame = ttk.Frame(history_dialog)
        filter_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(filter_frame, text="Filter:").pack(side=tk.LEFT, padx=(10, 5))

        # Status filter
        status_var = tk.StringVar(value="all")
        ttk.Radiobutton(filter_frame, text="All", variable=status_var, value="all").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(filter_frame, text="Sent", variable=status_var, value="sent").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(filter_frame, text="Failed", variable=status_var, value="failed").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(filter_frame, text="Pending", variable=status_var, value="pending").pack(side=tk.LEFT, padx=(0, 10))

        # Search
        search_frame = ttk.Frame(filter_frame)
        search_frame.pack(side=tk.RIGHT)
        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(5, 0))

        # History display
        history_frame = ttk.Frame(history_dialog)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # Treeview for message history
        columns = ("datetime", "username", "message", "status", "type")
        history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=15)

        # Configure columns
        history_tree.heading("datetime", text="Date/Time")
        history_tree.heading("username", text="Recipient")
        history_tree.heading("message", text="Message")
        history_tree.heading("status", text="Status")
        history_tree.heading("type", text="Type")

        history_tree.column("datetime", width=150, minwidth=120)
        history_tree.column("username", width=100, minwidth=80)
        history_tree.column("message", width=300, minwidth=200)
        history_tree.column("status", width=80, minwidth=60)
        history_tree.column("type", width=80, minwidth=60)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=history_tree.yview)
        h_scrollbar = ttk.Scrollbar(history_frame, orient=tk.HORIZONTAL, command=history_tree.xview)
        history_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Stats frame
        stats_frame = ttk.Frame(history_dialog)
        stats_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        stats_label = ttk.Label(stats_frame, text="Loading history...")
        stats_label.pack(side=tk.LEFT)

        # Buttons
        button_frame = ttk.Frame(history_dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(button_frame, text="📋 Export History", command=lambda: self._export_message_history(history_tree)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🗑️ Clear History", command=lambda: self._clear_message_history(history_dialog)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Close", command=history_dialog.destroy).pack(side=tk.RIGHT)

        # Load history
        def load_history():
            self._load_message_history(history_tree, stats_label, status_var.get(), search_var.get())

        # Bind search and filter updates
        def on_filter_change(*args):
            load_history()

        status_var.trace('w', on_filter_change)
        search_var.trace('w', lambda *args: history_dialog.after(500, load_history))  # Debounce search

        # Initial load
        load_history()

        # Store references for refresh
        history_dialog.history_tree = history_tree
        history_dialog.stats_label = stats_label
        history_dialog.status_var = status_var
        history_dialog.search_var = search_var

    def _load_message_history(self, tree, stats_label, status_filter, search_text):
        """Load message history into the treeview."""
        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        if not self.messaging:
            stats_label.config(text="No messaging system available")
            return

        # Get history from messaging system
        history = getattr(self.messaging, 'task_history', [])

        # Filter and sort history (most recent first)
        filtered_history = []
        for task in history:
            # Status filter
            if status_filter != "all" and task.status != status_filter:
                continue

            # Search filter
            if search_text:
                search_lower = search_text.lower()
                if (search_lower not in task.username.lower() and
                    search_lower not in task.message.lower()):
                    continue

            filtered_history.append(task)

        # Sort by creation time (newest first)
        filtered_history.sort(key=lambda x: x.created_at, reverse=True)

        # Add to treeview
        for task in filtered_history:
            datetime_str = task.created_at.strftime("%Y-%m-%d %H:%M:%S")
            message_preview = task.message[:50] + "..." if len(task.message) > 50 else task.message
            status_display = task.status.title()
            if task.error_message:
                status_display += " (Error)"

            tree.insert("", tk.END, values=(
                datetime_str,
                f"@{task.username}",
                message_preview,
                status_display,
                task.message_type.value.title()
            ))

        # Update stats
        total = len(filtered_history)
        sent = sum(1 for t in filtered_history if t.status == "sent")
        failed = sum(1 for t in filtered_history if t.status == "failed")
        pending = sum(1 for t in filtered_history if t.status == "pending")

        stats_label.config(text=f"Total: {total} | Sent: {sent} | Failed: {failed} | Pending: {pending}")

    def _refresh_message_history(self, dialog):
        """Refresh message history display."""
        if hasattr(dialog, 'history_tree') and hasattr(dialog, 'stats_label'):
            status_filter = getattr(dialog, 'status_var', tk.StringVar(value="all")).get()
            search_text = getattr(dialog, 'search_var', tk.StringVar()).get()
            self._load_message_history(dialog.history_tree, dialog.stats_label, status_filter, search_text)

    def _export_message_history(self, tree):
        """Export message history to CSV."""
        try:
            from tkinter import filedialog
            import csv

            file_path = filedialog.asksaveasfilename(
                title="Export Message History",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(["Date/Time", "Username", "Message", "Status", "Type"])

                    for item in tree.get_children():
                        values = tree.item(item, "values")
                        writer.writerow(values)

                messagebox.showinfo("Export Complete", f"Message history exported to {file_path}")

        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export history: {e}")

    def _clear_message_history(self, dialog):
        """Clear message history after confirmation."""
        if messagebox.askyesno("Confirm Clear", "Are you sure you want to clear all message history?\nThis action cannot be undone."):
            try:
                if self.messaging:
                    self.messaging.task_history.clear()
                    self.messaging._save_task_history()

                # Refresh display
                self._refresh_message_history(dialog)
                messagebox.showinfo("History Cleared", "Message history has been cleared.")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to clear history: {e}")

    def show_message_settings(self):
        """Show message settings dialog."""
        settings_dialog = tk.Toplevel(self.root)
        settings_dialog.title("Message Settings")
        settings_dialog.geometry("500x400")

        # Settings content
        ttk.Label(settings_dialog, text="Message Settings", font=('Arial', 12, 'bold')).pack(pady=10)

        settings_frame = ttk.Frame(settings_dialog, padding=20)
        settings_frame.pack(fill=tk.BOTH, expand=True)

        # Load existing settings
        current_settings = self._load_message_settings()

        # Rate limiting settings
        rate_frame = ttk.LabelFrame(settings_frame, text="Rate Limiting", padding=10)
        rate_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(rate_frame, text="Messages per hour (Instagram limit: 200):").pack(anchor=tk.W)
        self.rate_limit_var = tk.IntVar(value=current_settings.get("rate_limit_per_hour", 180))
        ttk.Spinbox(rate_frame, from_=1, to=200, textvariable=self.rate_limit_var).pack(anchor=tk.W, pady=(5, 0))

        # Delay settings
        delay_frame = ttk.LabelFrame(settings_frame, text="Delays", padding=10)
        delay_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(delay_frame, text="Delay between messages (seconds):").pack(anchor=tk.W)
        self.delay_var = tk.IntVar(value=current_settings.get("delay_between_messages", 30))
        ttk.Spinbox(delay_frame, from_=10, to=300, textvariable=self.delay_var).pack(anchor=tk.W, pady=(5, 0))

        # Error handling
        error_frame = ttk.LabelFrame(settings_frame, text="Error Handling", padding=10)
        error_frame.pack(fill=tk.X, pady=(0, 10))

        self.retry_var = tk.BooleanVar(value=current_settings.get("auto_retry_failed", True))
        ttk.Checkbutton(error_frame, text="Auto-retry failed messages", variable=self.retry_var).pack(anchor=tk.W)

        ttk.Label(error_frame, text="Max retries:").pack(anchor=tk.W, pady=(5, 0))
        self.max_retries_var = tk.IntVar(value=current_settings.get("max_retries", 3))
        ttk.Spinbox(error_frame, from_=0, to=10, textvariable=self.max_retries_var).pack(anchor=tk.W)

        # Buttons
        button_frame = ttk.Frame(settings_dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        ttk.Button(button_frame, text="Save Settings", command=lambda: self.save_message_settings(settings_dialog),
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=settings_dialog.destroy).pack(side=tk.RIGHT)

    def _load_message_settings(self):
        """Load message settings from file."""
        try:
            import json
            from pathlib import Path

            settings_file = Path("message_settings.json")
            if not settings_file.exists():
                return {}

            with open(settings_file, 'r', encoding='utf-8') as f:
                all_settings = json.load(f)

            # Get settings for current account or global
            account_id = self.current_account.account_id if self.current_account else "global"
            return all_settings.get(account_id, {})

        except Exception as e:
            logger.warning(f"Could not load message settings: {e}")
            return {}

    def save_message_settings(self, dialog):
        """Save message settings."""
        try:
            import json
            from pathlib import Path

            # Get settings values
            settings = {
                "rate_limit_per_hour": self.rate_limit_var.get(),
                "delay_between_messages": self.delay_var.get(),
                "auto_retry_failed": self.retry_var.get(),
                "max_retries": self.max_retries_var.get(),
                "last_updated": datetime.now().isoformat(),
                "account_id": self.current_account.account_id if self.current_account else "global"
            }

            # Save to file
            settings_file = Path("message_settings.json")
            existing_settings = {}

            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    existing_settings = json.load(f)

            # Update settings for current account
            account_id = settings["account_id"]
            existing_settings[account_id] = settings

            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(existing_settings, f, indent=2, ensure_ascii=False)

            # Apply settings to messaging system if available
            if self.messaging:
                self.messaging.rate_limiter.requests_per_hour = settings["rate_limit_per_hour"]
                # Note: Other settings would need to be applied to the messaging system

            messagebox.showinfo("Settings", "Message settings saved successfully!")
            dialog.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")
            logger.error(f"Settings save error: {e}")

    def on_schedule_option_change(self, *args):
        """Handle schedule option changes."""
        option = self.schedule_var.get()

        # Clear existing schedule picker frame
        for widget in self.schedule_picker_frame.winfo_children():
            widget.destroy()

        if option == "now":
            # Hide schedule picker
            self.schedule_picker_frame.pack_forget()
        elif option == "schedule":
            # Show date/time picker for one-time scheduling
            self.create_schedule_picker()
            self.schedule_picker_frame.pack(fill=tk.X, pady=(10, 0))
        elif option == "recurring":
            # Show recurring options
            self.create_recurring_picker()
            self.schedule_picker_frame.pack(fill=tk.X, pady=(10, 0))

    def create_schedule_picker(self):
        """Create one-time schedule picker."""
        # Date selection
        date_frame = ttk.Frame(self.schedule_picker_frame)
        date_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(date_frame, text="Date:").pack(side=tk.LEFT)
        self.schedule_date_var = tk.StringVar()
        date_entry = ttk.Entry(date_frame, textvariable=self.schedule_date_var, width=12)
        date_entry.pack(side=tk.LEFT, padx=(10, 5))
        date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        ttk.Button(date_frame, text="📅", width=3, command=self.pick_date).pack(side=tk.LEFT)

        # Time selection
        time_frame = ttk.Frame(self.schedule_picker_frame)
        time_frame.pack(fill=tk.X)

        ttk.Label(time_frame, text="Time:").pack(side=tk.LEFT)
        self.schedule_time_var = tk.StringVar()
        time_entry = ttk.Entry(time_frame, textvariable=self.schedule_time_var, width=8)
        time_entry.pack(side=tk.LEFT, padx=(10, 5))
        time_entry.insert(0, "12:00")

        # Timezone note
        ttk.Label(time_frame, text="(24h format, local timezone)",
                 font=('Arial', 8), foreground='gray').pack(side=tk.LEFT, padx=(5, 0))

    def create_recurring_picker(self):
        """Create recurring schedule picker."""
        # Frequency selection
        freq_frame = ttk.Frame(self.schedule_picker_frame)
        freq_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(freq_frame, text="Frequency:").pack(side=tk.LEFT)
        self.recurring_freq_var = tk.StringVar(value="daily")
        freq_combo = ttk.Combobox(freq_frame, textvariable=self.recurring_freq_var,
                                 values=["daily", "weekly", "monthly"], state="readonly", width=10)
        freq_combo.pack(side=tk.LEFT, padx=(10, 5))

        # Time selection for recurring
        time_frame = ttk.Frame(self.schedule_picker_frame)
        time_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(time_frame, text="Time:").pack(side=tk.LEFT)
        self.recurring_time_var = tk.StringVar()
        time_entry = ttk.Entry(time_frame, textvariable=self.recurring_time_var, width=8)
        time_entry.pack(side=tk.LEFT, padx=(10, 5))
        time_entry.insert(0, "12:00")

        # Duration
        duration_frame = ttk.Frame(self.schedule_picker_frame)
        duration_frame.pack(fill=tk.X)

        ttk.Label(duration_frame, text="Duration:").pack(side=tk.LEFT)
        self.recurring_duration_var = tk.IntVar(value=7)
        ttk.Spinbox(duration_frame, from_=1, to=365, textvariable=self.recurring_duration_var,
                   width=5).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Label(duration_frame, text="days", font=('Arial', 8)).pack(side=tk.LEFT)

    def pick_date(self):
        """Open date picker dialog."""
        try:
            from tkcalendar import DateEntry
            import tkinter as tk

            # Create a popup window for date selection
            date_dialog = tk.Toplevel(self.root)
            date_dialog.title("Select Date")
            date_dialog.geometry("300x300")
            date_dialog.resizable(False, False)

            ttk.Label(date_dialog, text="Select Date", font=('Arial', 12, 'bold')).pack(pady=10)

            # Create calendar widget
            cal = DateEntry(date_dialog, width=12, background='darkblue',
                           foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
            cal.pack(pady=10)

            # Set current date from entry if valid
            try:
                current_date = datetime.strptime(self.schedule_date_var.get(), "%Y-%m-%d").date()
                cal.set_date(current_date)
            except:
                cal.set_date(datetime.now().date())

            def select_date():
                selected_date = cal.get_date()
                self.schedule_date_var.set(selected_date.strftime("%Y-%m-%d"))
                date_dialog.destroy()

            ttk.Button(date_dialog, text="Select Date", command=select_date,
                      style='Primary.TButton').pack(pady=10)

            # Center the dialog
            date_dialog.transient(self.root)
            date_dialog.grab_set()

        except ImportError:
            # Fallback to manual entry with better validation
            date_dialog = tk.Toplevel(self.root)
            date_dialog.title("Enter Date")
            date_dialog.geometry("350x200")

            ttk.Label(date_dialog, text="Enter Date (YYYY-MM-DD)", font=('Arial', 12, 'bold')).pack(pady=10)

            date_var = tk.StringVar(value=self.schedule_date_var.get())

            def validate_date():
                try:
                    datetime.strptime(date_var.get(), "%Y-%m-%d")
                    self.schedule_date_var.set(date_var.get())
                    date_dialog.destroy()
                except ValueError:
                    messagebox.showerror("Invalid Date", "Please enter date in YYYY-MM-DD format\nExample: 2024-12-25")

            entry = ttk.Entry(date_dialog, textvariable=date_var, width=20, font=('Arial', 12))
            entry.pack(pady=5)
            entry.focus()
            entry.select_range(0, tk.END)

            ttk.Button(date_dialog, text="OK", command=validate_date,
                      style='Primary.TButton').pack(pady=10)

            date_dialog.bind('<Return>', lambda e: validate_date())
            date_dialog.bind('<Escape>', lambda e: date_dialog.destroy())


class AccountDialog:
    """Dialog for adding/editing accounts."""
    
    def __init__(self, parent, account_manager):
        self.result = None
        self.account_manager = account_manager
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add Instagram Account")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_dialog()
        
        # Center dialog
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))

    def create_dialog(self):
        """Create dialog interface."""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Form fields
        fields = [
            ("Account Name:", "name"),
            ("Instagram Username:", "username"),
            ("Instagram Password:", "password")
        ]
        
        self.entries = {}
        
        for i, (label_text, field_name) in enumerate(fields):
            ttk.Label(main_frame, text=label_text).grid(row=i, column=0, sticky='w', pady=5)
            
            if field_name == "password":
                entry = ttk.Entry(main_frame, show="*", width=30)
            else:
                entry = ttk.Entry(main_frame, width=30)
            
            entry.grid(row=i, column=1, sticky='ew', pady=5, padx=(10, 0))
            self.entries[field_name] = entry
        
        # Options
        ttk.Label(main_frame, text="Options:").grid(row=len(fields), column=0, sticky='w', pady=(20, 5))
        
        options_frame = ttk.Frame(main_frame)
        options_frame.grid(row=len(fields), column=1, sticky='w', pady=(20, 5), padx=(10, 0))
        
        self.active_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Active", variable=self.active_var).pack(anchor='w')
        
        self.auto_post_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="Auto-posting", variable=self.auto_post_var).pack(anchor='w')
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=len(fields) + 2, column=0, columnspan=2, pady=(30, 0))
        
        ttk.Button(button_frame, text="Add Account", command=self.add_account,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self.cancel).pack(side=tk.LEFT)
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)

    def add_account(self):
        """Add the account."""
        name = self.entries["name"].get().strip()
        username = self.entries["username"].get().strip()
        password = self.entries["password"].get()
        
        if not name or not username:
            messagebox.showerror("Error", "Please fill in all required fields")
            return
        
        success = self.account_manager.add_account(
            name=name,
            username=username,
            password=password,
            is_active=self.active_var.get(),
            auto_post=self.auto_post_var.get()
        )
        
        if success:
            self.result = {
                "name": name,
                "username": username,
                "is_active": self.active_var.get(),
                "auto_post": self.auto_post_var.get()
            }
            self.dialog.destroy()
        else:
            messagebox.showerror("Error", "Failed to add account. Name might already exist.")

    def cancel(self):
        """Cancel dialog."""
        self.dialog.destroy()


class SchedulingDialog:
    """Dialog for scheduling posts with advanced options."""
    
    def __init__(self, parent, schedule_manager, video_items, account_id):
        self.result = None
        self.schedule_manager = schedule_manager
        self.video_items = video_items
        self.account_id = account_id
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Schedule Posts")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_dialog()
        
        # Center dialog
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))

    def create_dialog(self):
        """Create scheduling dialog interface."""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        ttk.Label(main_frame, text=f"Schedule {len(self.video_items)} Videos", 
                 style='Title.TLabel').pack(pady=(0, 20))
        
        # Scheduling options
        options_frame = ttk.LabelFrame(main_frame, text="Scheduling Options", padding="10")
        options_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.schedule_type_var = tk.StringVar(value="draft")
        
        options = [
            ("draft", "💾 Save as Drafts", "Prepare for manual editing in Instagram app"),
            ("immediately", "⚡ Post Immediately", "Post all videos right now"),
            ("schedule_delay", "⏰ Schedule for Later", "Set specific time for posting"),
            ("optimal_time", "📈 Optimal Time", "Post at the best engagement time")
        ]
        
        for value, title, description in options:
            frame = ttk.Frame(options_frame)
            frame.pack(fill=tk.X, pady=5)
            
            ttk.Radiobutton(frame, text=title, variable=self.schedule_type_var, 
                           value=value).pack(side=tk.LEFT)
            ttk.Label(frame, text=description, font=('Segoe UI', 8)).pack(side=tk.LEFT, padx=(10, 0))
        
        # Time selection
        self.time_frame = ttk.LabelFrame(main_frame, text="Schedule Time", padding="10")
        self.time_frame.pack(fill=tk.X, pady=(0, 20))
        
        # DateTime selection
        time_controls = ttk.Frame(self.time_frame)
        time_controls.pack(fill=tk.X)
        
        ttk.Label(time_controls, text="Start Time:").pack(side=tk.LEFT)
        
        # Simple time entry
        self.time_var = tk.StringVar(value="19:00")  # Default 7 PM
        ttk.Entry(time_controls, textvariable=self.time_var, width=10).pack(side=tk.LEFT, padx=(10, 5))
        
        ttk.Label(time_controls, text="Interval (minutes):").pack(side=tk.LEFT, padx=(20, 5))
        self.interval_var = tk.IntVar(value=30)
        ttk.Spinbox(time_controls, from_=5, to=240, width=10, 
                   textvariable=self.interval_var).pack(side=tk.LEFT, padx=(5, 0))
        
        # Summary
        summary_frame = ttk.LabelFrame(main_frame, text="Summary", padding="10")
        summary_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.summary_label = ttk.Label(summary_frame, text="Select scheduling option above")
        self.summary_label.pack()
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="✅ Schedule", command=self.schedule,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Cancel", command=self.cancel).pack(side=tk.LEFT)
        
        # Bind events
        self.schedule_type_var.trace('w', self.update_summary)
        self.interval_var.trace('w', self.update_summary)
        
        # Initial summary update
        self.update_summary()

    def update_summary(self, *args):
        """Update the summary based on current selections."""
        schedule_type = self.schedule_type_var.get()
        interval = self.interval_var.get()
        
        if schedule_type == "draft":
            summary = f"Prepare {len(self.video_items)} videos as drafts for manual posting"
        elif schedule_type == "immediately":
            summary = f"Post {len(self.video_items)} videos immediately"
        elif schedule_type == "schedule_delay":
            total_time = (len(self.video_items) - 1) * interval
            summary = f"Schedule {len(self.video_items)} videos starting at {self.time_var.get()}, {interval} minutes apart\nTotal duration: {total_time} minutes"
        elif schedule_type == "optimal_time":
            summary = f"Schedule {len(self.video_items)} videos at optimal engagement times"
        else:
            summary = "Select a scheduling option"
        
        self.summary_label.config(text=summary)

    def schedule(self):
        """Execute the scheduling."""
        try:
            schedule_type = self.schedule_type_var.get()
            
            # Schedule the videos
            result = self.schedule_manager.schedule_bulk_videos(
                video_items=self.video_items,
                account_id=self.account_id,
                schedule_type=schedule_type,
                interval_minutes=self.interval_var.get()
            )
            
            if result['success']:
                self.result = result
                messagebox.showinfo("Success", 
                    f"Successfully scheduled {result['scheduled_count']} videos!")
                self.dialog.destroy()
            else:
                messagebox.showerror("Error", "Failed to schedule videos")
                
        except Exception as e:
            messagebox.showerror("Error", f"Scheduling failed: {e}")

    def cancel(self):
        """Cancel the dialog."""
        self.dialog.destroy()


if __name__ == "__main__":
    root = tk.Tk()
    app = ModernAutoposterGUI(root)
    root.mainloop()
