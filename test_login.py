#!/usr/bin/env python3
"""
Test script to debug login issues
"""
import sys
import os
import json
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.getcwd())

from account_manager import Account<PERSON>anager, AccountConfig
from config import ConfigManager
from auth import AuthManager

def test_config_loading():
    """Test config loading"""
    print("=== Testing Config Loading ===")
    
    # Test account config
    config_path = "accounts/hh122.11/config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        print(f"Config file content: {config_data}")
        
        # Test ConfigManager
        config_manager = ConfigManager(config_path)
        print(f"ConfigManager username: '{config_manager.username}'")
        print(f"ConfigManager password: '{config_manager.password}'")
        
        # Test credentials section
        creds = config_data.get('credentials', {})
        print(f"Credentials section: {creds}")
        
        # Test root level
        print(f"Root username: '{config_data.get('username', '')}'")
        print(f"Root password: '{config_data.get('password', '')}'")
    else:
        print(f"Config file not found: {config_path}")

def test_account_manager():
    """Test account manager"""
    print("\n=== Testing Account Manager ===")
    
    try:
        account_manager = AccountManager()
        print(f"Loaded accounts: {list(account_manager.accounts.keys())}")
        
        if account_manager.accounts:
            account_id = list(account_manager.accounts.keys())[0]
            account = account_manager.accounts[account_id]
            print(f"Account ID: {account_id}")
            print(f"Account username: '{account.config.username}'")
            print(f"Account password: {'*' * len(account.config.password) if account.config.password else 'None'}")
            
            # Test config manager
            print(f"Config manager username: '{account.config_manager.username}'")
            print(f"Config manager password: {'*' * len(account.config_manager.password) if account.config_manager.password else 'None'}")
            
            return account
        else:
            print("No accounts found")
            return None
    except Exception as e:
        print(f"Error loading account manager: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_login(account):
    """Test login functionality"""
    print("\n=== Testing Login ===")
    
    if not account:
        print("No account to test")
        return
    
    try:
        print(f"Attempting login for: {account.config.username}")
        success = account.login()
        print(f"Login result: {success}")
        
        if not success:
            print(f"Last error: {account.last_error}")
            
    except Exception as e:
        print(f"Login error: {e}")
        import traceback
        traceback.print_exc()

def test_verify(account):
    """Test verify functionality"""
    print("\n=== Testing Verify ===")
    
    if not account:
        print("No account to test")
        return
    
    try:
        print(f"Attempting verify for: {account.config.username}")
        success = account.verify_login()
        print(f"Verify result: {success}")
        
        if not success:
            print(f"Last error: {account.last_error}")
            
    except Exception as e:
        print(f"Verify error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Instagram Login Debug Test")
    print("=" * 50)
    
    # Test config loading
    test_config_loading()
    
    # Test account manager
    account = test_account_manager()
    
    # Test login
    test_login(account)
    
    # Test verify
    test_verify(account)
    
    print("\n" + "=" * 50)
    print("Test completed")
