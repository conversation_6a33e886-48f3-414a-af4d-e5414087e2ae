"""
AI Caption Generator using OpenRouter API
Generates engaging captions for Instagram Reels
"""
import logging
import json
import requests
import os
from typing import Optional, Dict, List, Any, Callable
from pathlib import Path
import time
import hashlib

logger = logging.getLogger(__name__)


class CaptionGenerator:
    """AI-powered caption generator using OpenRouter API with enhanced free model support."""
    
    def __init__(self, config_manager=None, api_key: Optional[str] = None):
        self.config_manager = config_manager
        self.api_key = api_key or (config_manager.openrouter_api_key if config_manager else None) or os.getenv("OPENROUTER_API_KEY")
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.models_url = "https://openrouter.ai/api/v1/models"
        
        # Get model preferences from config or use defaults
        if config_manager:
            self.default_model = config_manager.openrouter_default_model
            self.fallback_model = config_manager.openrouter_fallback_model
        else:
            self.default_model = "meta-llama/llama-3.2-3b-instruct:free"
            self.fallback_model = "meta-llama/llama-3.2-1b-instruct:free"
        
        # Available free models for selection
        self.free_models = {
            'meta-llama/llama-3.2-3b-instruct:free': 'Llama 3.2 3B (Free) - Best for general captions',
            'meta-llama/llama-3.2-1b-instruct:free': 'Llama 3.2 1B (Free) - Fast and efficient',
            'meta-llama/llama-3.1-8b-instruct:free': 'Llama 3.1 8B (Free) - More creative',
            'google/gemma-2-9b-it:free': 'Gemma 2 9B (Free) - Good for engaging content',
            'microsoft/phi-3-mini-128k-instruct:free': 'Phi-3 Mini (Free) - Concise and professional',
            'microsoft/phi-3-medium-128k-instruct:free': 'Phi-3 Medium (Free) - Balanced performance'
        }
        
        # Caption style templates
        self.caption_styles = {
            "engaging": {
                "description": "Energetic and engaging with emojis",
                "prompt_template": "Create an engaging Instagram reel caption that hooks viewers and encourages interaction. Use emojis, ask questions, and include relevant hashtags. Keep it conversational and exciting."
            },
            "professional": {
                "description": "Professional and informative",
                "prompt_template": "Create a professional Instagram reel caption that's informative and authoritative. Focus on value and expertise. Use minimal emojis and strategic hashtags."
            },
            "casual": {
                "description": "Friendly and casual tone",
                "prompt_template": "Create a casual, friendly Instagram reel caption that feels like talking to a friend. Be relatable and authentic with natural language and appropriate hashtags."
            },
            "funny": {
                "description": "Humorous and entertaining",
                "prompt_template": "Create a funny, entertaining Instagram reel caption that makes people smile. Use humor, wordplay, or witty observations with fun emojis and hashtags."
            },
            "motivational": {
                "description": "Inspiring and motivational",
                "prompt_template": "Create an inspiring, motivational Instagram reel caption that uplifts and encourages. Use powerful words, inspiring quotes, and motivational hashtags."
            },
            "educational": {
                "description": "Educational and informative",
                "prompt_template": "Create an educational Instagram reel caption that teaches something valuable. Break down complex topics simply and include educational hashtags."
            }
        }
        
        # Load configuration from config manager or file
        if config_manager:
            self.config = self._load_config_from_manager()
        else:
            self.config = self.load_config()
        
        # Cache for generated captions
        self.cache = {}
        self.cache_file = "caption_cache.json"
        self.load_cache()
        
        # Available models cache
        self.available_models = {}
        self.models_cache_file = "openrouter_models_cache.json"
        self.load_models_cache()
    
    def _load_config_from_manager(self) -> Dict[str, Any]:
        """Load configuration from config manager."""
        return {
            "default_style": self.config_manager.caption_default_style,
            "max_caption_length": self.config_manager.get('caption_generation.max_caption_length', 2200),
            "include_hashtags": self.config_manager.get('caption_generation.include_hashtags', True),
            "max_hashtags": self.config_manager.get('caption_generation.max_hashtags', 25),
            "custom_instructions": self.config_manager.get('caption_generation.custom_instructions', ''),
            "temperature": self.config_manager.get('openrouter.temperature', 0.7),
            "models": {
                "primary": self.config_manager.openrouter_default_model,
                "fallback": self.config_manager.openrouter_fallback_model
            },
            "bulk_generation_enabled": self.config_manager.bulk_generation_enabled,
            "auto_generate_on_scan": self.config_manager.auto_generate_captions
        }

    def load_config(self) -> Dict[str, Any]:
        """Load caption generator configuration from file."""
        default_config = {
            "default_style": "engaging",
            "max_caption_length": 2200,
            "include_hashtags": True,
            "max_hashtags": 25,
            "custom_instructions": "",
            "temperature": 0.7,
            "models": {
                "primary": "meta-llama/llama-3.2-3b-instruct:free",
                "fallback": "meta-llama/llama-3.2-1b-instruct:free"
            },
            "bulk_generation_enabled": True,
            "auto_generate_on_scan": False
        }
        
        try:
            if hasattr(self, 'config_file') and os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    # Merge with defaults
                    default_config.update(config)
            return default_config
        except Exception as e:
            logger.warning(f"Could not load caption config: {e}")
            return default_config
    
    def save_config(self) -> None:
        """Save caption generator configuration."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save caption config: {e}")
    
    def load_cache(self) -> None:
        """Load caption cache from file."""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    self.cache = json.load(f)
        except Exception as e:
            logger.warning(f"Could not load caption cache: {e}")
            self.cache = {}
    
    def save_cache(self) -> None:
        """Save caption cache to file."""
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(self.cache, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save caption cache: {e}")
    
    def load_models_cache(self) -> None:
        """Load models cache from file."""
        try:
            if os.path.exists(self.models_cache_file):
                with open(self.models_cache_file, 'r') as f:
                    cache_data = json.load(f)
                    # Check if cache is recent (less than 24 hours old)
                    if time.time() - cache_data.get('timestamp', 0) < 86400:
                        self.available_models = cache_data.get('models', {})
                        logger.info("Loaded models from cache")
                    else:
                        logger.info("Models cache expired")
        except Exception as e:
            logger.warning(f"Could not load models cache: {e}")
    
    def save_models_cache(self) -> None:
        """Save models cache to file."""
        try:
            cache_data = {
                'timestamp': time.time(),
                'models': self.available_models
            }
            with open(self.models_cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save models cache: {e}")
    
    def fetch_available_models(self) -> Dict[str, Any]:
        """Fetch available models from OpenRouter API."""
        if not self.api_key:
            logger.warning("No API key available for fetching models")
            return self.free_models
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(self.models_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            models_data = response.json()
            
            # Process models and filter for free ones
            available_models = {}
            for model in models_data.get('data', []):
                model_id = model.get('id', '')
                model_name = model.get('name', model_id)
                pricing = model.get('pricing', {})
                
                # Check if model is free
                prompt_cost = float(pricing.get('prompt', '0'))
                completion_cost = float(pricing.get('completion', '0'))
                
                if prompt_cost == 0 and completion_cost == 0:
                    available_models[model_id] = f"{model_name} (Free)"
                elif model_id.endswith(':free'):
                    available_models[model_id] = f"{model_name} (Free)"
            
            # Merge with known free models
            available_models.update(self.free_models)
            
            # Cache the results
            self.available_models = available_models
            self.save_models_cache()
            
            logger.info(f"Fetched {len(available_models)} available models")
            return available_models
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch models from OpenRouter: {e}")
            return self.free_models
        except Exception as e:
            logger.error(f"Error processing models data: {e}")
            return self.free_models
    
    def get_available_models(self, force_refresh: bool = False) -> Dict[str, str]:
        """Get available models with caching."""
        if force_refresh or not self.available_models:
            return self.fetch_available_models()
        return self.available_models
    
    def generate_cache_key(self, video_path: str, style: str, custom_prompt: str = "") -> str:
        """Generate cache key for caption."""
        key_data = f"{video_path}:{style}:{custom_prompt}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def analyze_video_content(self, video_path: str) -> Dict[str, Any]:
        """Analyze video content to help with caption generation."""
        path = Path(video_path)
        
        analysis = {
            "filename": path.name,
            "file_size_mb": round(path.stat().st_size / (1024 * 1024), 2),
            "content_hints": [],
            "suggested_topics": []
        }
        
        # Extract content hints from filename
        filename_lower = path.stem.lower()
        
        # Fitness/Workout keywords
        fitness_keywords = ["workout", "fitness", "gym", "exercise", "training", "muscle", "cardio", "strength"]
        if any(keyword in filename_lower for keyword in fitness_keywords):
            analysis["content_hints"].append("fitness")
            analysis["suggested_topics"].extend(["#fitness", "#workout", "#gym", "#training", "#health"])
        
        # Lifestyle keywords
        lifestyle_keywords = ["lifestyle", "daily", "routine", "morning", "vlog", "day"]
        if any(keyword in filename_lower for keyword in lifestyle_keywords):
            analysis["content_hints"].append("lifestyle")
            analysis["suggested_topics"].extend(["#lifestyle", "#daily", "#routine", "#life"])
        
        # Dance/Music keywords
        dance_keywords = ["dance", "music", "beat", "rhythm", "choreography"]
        if any(keyword in filename_lower for keyword in dance_keywords):
            analysis["content_hints"].append("dance")
            analysis["suggested_topics"].extend(["#dance", "#music", "#reels", "#trending"])
        
        # Food keywords
        food_keywords = ["food", "recipe", "cooking", "eat", "meal", "kitchen"]
        if any(keyword in filename_lower for keyword in food_keywords):
            analysis["content_hints"].append("food")
            analysis["suggested_topics"].extend(["#food", "#recipe", "#cooking", "#foodie"])
        
        # If no specific category detected, use general
        if not analysis["content_hints"]:
            analysis["content_hints"].append("general")
            analysis["suggested_topics"].extend(["#reels", "#viral", "#explore", "#instagram"])
        
        return analysis
    
    def create_caption_prompt(self, video_path: str, style: str, custom_prompt: str = "") -> str:
        """Create the prompt for caption generation."""
        video_analysis = self.analyze_video_content(video_path)
        style_template = self.caption_styles.get(style, self.caption_styles["engaging"])
        
        # Build the prompt
        prompt = f"""
{style_template['prompt_template']}

Video Information:
- Filename: {video_analysis['filename']}
- Content Type: {', '.join(video_analysis['content_hints'])}
- Suggested Topics: {', '.join(video_analysis['suggested_topics'])}

Requirements:
- Maximum length: {self.config['max_caption_length']} characters
- Include relevant hashtags (max {self.config['max_hashtags']})
- Make it {style_template['description'].lower()}
- Focus on engagement and reach

{f'Additional Instructions: {custom_prompt}' if custom_prompt else ''}
{f'Custom Instructions: {self.config["custom_instructions"]}' if self.config.get("custom_instructions") else ''}

Generate only the caption text, no additional explanation.
"""
        
        return prompt.strip()
    
    def call_openrouter_api(self, prompt: str, model: Optional[str] = None, use_fallback: bool = True) -> Optional[str]:
        """Make API call to OpenRouter with fallback model support."""
        if not self.api_key:
            logger.error("OpenRouter API key not provided")
            return None
        
        model = model or self.config["models"]["primary"]
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/yourusername/instagram-autoposter",
            "X-Title": "Instagram Autoposter"
        }
        
        data = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": self.config.get("temperature", 0.7),
            "max_tokens": self.config.get("max_tokens", 500)
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if "choices" in result and len(result["choices"]) > 0:
                caption = result["choices"][0]["message"]["content"].strip()
                return caption
            else:
                logger.error(f"Unexpected API response format: {result}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"OpenRouter API request failed with {model}: {e}")
            
            # Try fallback model if enabled and available
            if use_fallback and model != self.config["models"]["fallback"]:
                logger.info(f"Trying fallback model: {self.config['models']['fallback']}")
                return self.call_openrouter_api(prompt, self.config["models"]["fallback"], use_fallback=False)
            
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse API response: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error calling OpenRouter API: {e}")
            return None
    
    def generate_caption(self, video_path: str, style: str = "engaging", 
                        custom_prompt: str = "", use_cache: bool = True) -> Dict[str, Any]:
        """Generate a caption for the video."""
        result = {
            "success": False,
            "caption": "",
            "style": style,
            "cached": False,
            "error": None,
            "video_analysis": {}
        }
        
        try:
            # Check cache first
            cache_key = self.generate_cache_key(video_path, style, custom_prompt)
            if use_cache and cache_key in self.cache:
                cached_result = self.cache[cache_key]
                result.update(cached_result)
                result["cached"] = True
                logger.info(f"Using cached caption for {Path(video_path).name}")
                return result
            
            # Analyze video
            result["video_analysis"] = self.analyze_video_content(video_path)
            
            # Create prompt
            prompt = self.create_caption_prompt(video_path, style, custom_prompt)
            
            # Generate caption
            caption = self.call_openrouter_api(prompt)
            
            if caption:
                # Validate caption length
                if len(caption) > self.config["max_caption_length"]:
                    caption = caption[:self.config["max_caption_length"]]
                    logger.warning("Caption truncated to fit Instagram limit")
                
                result["success"] = True
                result["caption"] = caption
                
                # Cache the result
                if use_cache:
                    self.cache[cache_key] = {
                        "success": True,
                        "caption": caption,
                        "style": style,
                        "video_analysis": result["video_analysis"],
                        "generated_at": time.time()
                    }
                    self.save_cache()
                
                logger.info(f"Generated {style} caption for {Path(video_path).name}")
            else:
                result["error"] = "Failed to generate caption from API"
                logger.error(f"Failed to generate caption for {video_path}")
        
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"Error generating caption: {e}")
        
        return result
    
    def generate_multiple_captions(self, video_path: str, styles: List[str]) -> Dict[str, Dict[str, Any]]:
        """Generate captions in multiple styles."""
        results = {}
        
        for style in styles:
            if style in self.caption_styles:
                results[style] = self.generate_caption(video_path, style)
            else:
                logger.warning(f"Unknown caption style: {style}")
        
        return results
    
    def generate_bulk_captions(self, video_files: List[str], style: str = "engaging", 
                              custom_prompt: str = "", progress_callback: Optional[Callable] = None,
                              model: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """Generate captions for multiple videos in bulk with progress tracking."""
        if not self.config.get("bulk_generation_enabled", True):
            logger.error("Bulk caption generation is disabled in configuration")
            return {}
        
        if not self.api_key:
            logger.error("OpenRouter API key not configured for bulk generation")
            return {}
        
        results = {}
        total_files = len(video_files)
        successful_generations = 0
        failed_generations = 0
        
        logger.info(f"Starting bulk caption generation for {total_files} videos")
        
        for i, video_path in enumerate(video_files):
            try:
                # Update progress
                if progress_callback:
                    progress_callback(i, total_files, f"Generating caption for {Path(video_path).name}")
                
                # Generate caption
                result = self.generate_caption(
                    video_path=video_path,
                    style=style,
                    custom_prompt=custom_prompt,
                    use_cache=True
                )
                
                results[video_path] = result
                
                if result['success']:
                    successful_generations += 1
                    logger.info(f"Generated caption for {Path(video_path).name}")
                else:
                    failed_generations += 1
                    logger.warning(f"Failed to generate caption for {Path(video_path).name}: {result.get('error', 'Unknown error')}")
                
                # Small delay to avoid overwhelming the API
                time.sleep(0.5)
                
            except Exception as e:
                failed_generations += 1
                logger.error(f"Error generating caption for {video_path}: {e}")
                results[video_path] = {
                    "success": False,
                    "caption": "",
                    "style": style,
                    "error": str(e)
                }
        
        # Final progress update
        if progress_callback:
            progress_callback(total_files, total_files, "Bulk generation completed")
        
        logger.info(f"Bulk caption generation completed: {successful_generations} successful, {failed_generations} failed")
        
        return results
    
    def generate_captions_for_queue_items(self, queue_items: List[Dict[str, Any]], 
                                        style: str = "engaging", model: Optional[str] = None,
                                        progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Generate captions for queue items and update them directly."""
        if not self.config.get("bulk_generation_enabled", True):
            return {"success": False, "error": "Bulk generation disabled"}
        
        total_items = len(queue_items)
        generated_count = 0
        updated_count = 0
        
        logger.info(f"Generating captions for {total_items} queue items")
        
        for i, item in enumerate(queue_items):
            try:
                # Update progress
                if progress_callback:
                    filename = Path(item['file']).name
                    progress_callback(i, total_items, f"Processing {filename}")
                
                # Skip if caption already exists and is not empty
                if item.get('caption') and item['caption'].strip() and len(item['caption'].strip()) > 10:
                    logger.info(f"Skipping {Path(item['file']).name} - caption already exists")
                    continue
                
                # Generate caption
                result = self.generate_caption(
                    video_path=item['file'],
                    style=style,
                    use_cache=True
                )
                
                if result['success']:
                    item['caption'] = result['caption']
                    generated_count += 1
                    updated_count += 1
                    logger.info(f"Generated caption for {Path(item['file']).name}")
                else:
                    logger.warning(f"Failed to generate caption for {Path(item['file']).name}: {result.get('error', 'Unknown')}")
                
                # Small delay
                time.sleep(0.3)
                
            except Exception as e:
                logger.error(f"Error processing queue item {item.get('file', 'unknown')}: {e}")
        
        # Final progress update
        if progress_callback:
            progress_callback(total_items, total_items, f"Generated {generated_count} captions")
        
        return {
            "success": True,
            "total_processed": total_items,
            "generated_count": generated_count,
            "updated_count": updated_count
        }
    
    def get_available_styles(self) -> Dict[str, str]:
        """Get available caption styles with descriptions."""
        return {
            style: data["description"] 
            for style, data in self.caption_styles.items()
        }
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """Update configuration settings."""
        self.config.update(updates)
        self.save_config()
        logger.info("Caption generator configuration updated")
    
    def test_api_connection(self) -> Dict[str, Any]:
        """Test OpenRouter API connection."""
        result = {
            "success": False,
            "error": None,
            "response_time": 0
        }
        
        if not self.api_key:
            result["error"] = "API key not provided"
            return result
        
        try:
            start_time = time.time()
            test_prompt = "Generate a simple test caption for an Instagram reel."
            
            caption = self.call_openrouter_api(test_prompt)
            
            result["response_time"] = round(time.time() - start_time, 2)
            
            if caption:
                result["success"] = True
                result["test_caption"] = caption[:100] + "..." if len(caption) > 100 else caption
            else:
                result["error"] = "API call succeeded but no caption returned"
                
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def clear_cache(self) -> None:
        """Clear the caption cache."""
        self.cache = {}
        if os.path.exists(self.cache_file):
            os.remove(self.cache_file)
        logger.info("Caption cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "total_entries": len(self.cache),
            "cache_file_exists": os.path.exists(self.cache_file),
            "cache_size_kb": round(len(json.dumps(self.cache)) / 1024, 2) if self.cache else 0
        }
