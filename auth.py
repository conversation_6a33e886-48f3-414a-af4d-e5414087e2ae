"""
Authentication handling for Instagram Reels Autoposter - Enhanced Version
"""
import logging
import os
import time
import json
import uuid
import random
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path
from instagrapi import Client
from instagrapi.exceptions import <PERSON><PERSON><PERSON>equired, <PERSON>lient<PERSON>rror, TwoFactorRequired, BadPassword, ChallengeRequired

logger = logging.getLogger(__name__)


class AuthManager:
    """Enhanced Instagram authentication and session management with robust error handling."""

    def __init__(self, session_file: str = 'session.json', config_manager=None):
        # Initialize client with better settings
        self.client = Client()
        self.session_file = session_file
        self.config_manager = config_manager
        self.username = ''
        self.password = ''
        self.is_logged_in = False
        
        # Generate unique device fingerprint for this account
        self.device_fingerprint = self._generate_device_fingerprint()
        
        # Set better user agent and device settings
        self._configure_client_settings()

    def _generate_device_fingerprint(self) -> Dict[str, Any]:
        """Generate unique device fingerprint for this account to avoid conflicts."""
        try:
            # Generate unique identifiers
            device_fingerprint = {
                "phone_id": str(uuid.uuid4()),
                "uuid": str(uuid.uuid4()),
                "client_session_id": str(uuid.uuid4()),
                "advertising_id": str(uuid.uuid4()),
                "android_device_id": f"android-{uuid.uuid4().hex[:16]}",
                "request_id": str(uuid.uuid4()),
                "tray_session_id": str(uuid.uuid4())
            }
            
            # Random device variations to avoid detection
            manufacturers = ["Samsung", "OnePlus", "Google", "Xiaomi", "Huawei", "LG"]
            models = ["Galaxy S21", "OnePlus 9", "Pixel 6", "Mi 11", "P40 Pro", "V60"]
            android_versions = [28, 29, 30, 31, 32, 33]
            android_releases = ["9", "10", "11", "12", "13", "14"]
            resolutions = ["1080x1920", "1080x2280", "1080x2400", "1440x2960", "1440x3120"]
            dpis = ["480dpi", "420dpi", "560dpi", "640dpi"]
            
            idx = random.randint(0, len(manufacturers) - 1)
            android_idx = random.randint(0, len(android_versions) - 1)
            
            device_fingerprint.update({
                "manufacturer": manufacturers[idx],
                "model": models[idx],
                "android_version": android_versions[android_idx],
                "android_release": android_releases[android_idx],
                "resolution": random.choice(resolutions),
                "dpi": random.choice(dpis),
                "cpu": random.choice(["qcom", "exynos", "mtk"]),
                "device_name": f"device{random.randint(1000, 9999)}"
            })
            
            return device_fingerprint
            
        except Exception as e:
            logger.warning(f"Could not generate device fingerprint: {e}")
            # Fallback to basic fingerprint
            return {
                "phone_id": str(uuid.uuid4()),
                "uuid": str(uuid.uuid4()),
                "android_device_id": f"android-{uuid.uuid4().hex[:16]}"
            }

    def _configure_client_settings(self) -> None:
        """Configure client with optimal settings for Instagram API using unique device fingerprint."""
        try:
            # Build user agent with unique device details
            fingerprint = self.device_fingerprint
            app_version = "283.0.0.18.111"
            version_code = random.randint(400000000, 500000000)
            
            user_agent = (f"Instagram {app_version} Android "
                         f"({fingerprint.get('android_version', 29)}/"
                         f"{fingerprint.get('android_release', '10')}; "
                         f"{fingerprint.get('dpi', '480dpi')}; "
                         f"{fingerprint.get('resolution', '1080x1920')}; "
                         f"{fingerprint.get('manufacturer', 'OnePlus')}; "
                         f"{fingerprint.get('model', 'OnePlus6')}; "
                         f"{fingerprint.get('device_name', 'OnePlus6')}; "
                         f"{fingerprint.get('cpu', 'qcom')}; en_US; {version_code})")
            
            # Set client settings with unique fingerprint
            settings = {
                "user_agent": user_agent,
                "country": "US",
                "country_code": 1,
                "locale": "en_US",
                "timezone_offset": random.randint(-28800, -14400),  # Random US timezone
                "uuids": {
                    "phone_id": fingerprint["phone_id"],
                    "uuid": fingerprint["uuid"],
                    "client_session_id": fingerprint.get("client_session_id", str(uuid.uuid4())),
                    "advertising_id": fingerprint.get("advertising_id", str(uuid.uuid4())),
                    "android_device_id": fingerprint["android_device_id"],
                    "request_id": fingerprint.get("request_id", str(uuid.uuid4())),
                    "tray_session_id": fingerprint.get("tray_session_id", str(uuid.uuid4()))
                },
                "device_settings": {
                    "app_version": app_version,
                    "android_version": fingerprint.get('android_version', 29),
                    "android_release": fingerprint.get('android_release', '10'),
                    "dpi": fingerprint.get('dpi', '480dpi'),
                    "resolution": fingerprint.get('resolution', '1080x1920'),
                    "manufacturer": fingerprint.get('manufacturer', 'OnePlus'),
                    "device": fingerprint.get('device_name', 'OnePlus6'),
                    "model": fingerprint.get('model', 'OnePlus6'),
                    "cpu": fingerprint.get('cpu', 'qcom'),
                    "version_code": str(version_code)
                }
            }
            
            # Apply unique settings to client
            self.client.set_settings(settings)
            
            # Apply delay settings to avoid rate limiting
            self.client.delay_range = [1, 3]
            logger.info(f"Client settings configured with unique fingerprint for {fingerprint.get('manufacturer', 'OnePlus')} {fingerprint.get('model', 'OnePlus6')}")
            
        except Exception as e:
            logger.warning(f"Could not configure client settings: {e}")

    def set_credentials(self, username: str, password: str) -> None:
        """Set login credentials with validation."""
        if not username or not password:
            logger.warning("Cannot set empty credentials")
            return
            
        self.username = username
        self.password = password
        
        if self.config_manager:
            # Safely encode and store credentials
            try:
                self.config_manager.username = username
                self.config_manager.password = password
                self.config_manager.save_config()
                logger.info(f"Credentials saved for user: {username}")
            except Exception as e:
                logger.error(f"Failed to save credentials: {e}")

    def load_session(self) -> bool:
        """Load existing session from file with enhanced error handling and automatic restoration."""
        if not os.path.exists(self.session_file):
            logger.info("No session file found.")
            return False
            
        try:
            # Check file size and modification time
            session_stat = os.stat(self.session_file)
            if session_stat.st_size == 0:
                logger.warning("Empty session file")
                return False
                
            # Check if session file is too old (older than 30 days)
            session_age_days = (time.time() - session_stat.st_mtime) / (24 * 3600)
            if session_age_days > 30:
                logger.warning(f"Session file is {session_age_days:.1f} days old, may be expired")
            
            # Load settings from file
            settings = self.client.load_settings(self.session_file)
            if not settings:
                logger.warning("Could not load settings from session file")
                return False
            
            # Extract and restore device fingerprint from session if available
            if isinstance(settings, dict) and 'device_fingerprint' in settings:
                self.device_fingerprint = settings['device_fingerprint']
                logger.info("Device fingerprint restored from session")
                # Reapply client settings with restored fingerprint
                self._configure_client_settings()
            
            logger.info("Session settings loaded successfully")
            
            # Try to verify the session is still valid
            if self.config_manager and self.config_manager.username and self.config_manager.password:
                success = self._attempt_session_login()
                if success:
                    logger.info("Session restored and verified successfully")
                    # Save the session again to update timestamps
                    self._save_session_with_fingerprint()
                return success
            else:
                logger.warning("No credentials available for session verification")
                return False
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid session file format: {e}")
            self._backup_and_clear_session()
            return False
        except Exception as e:
            logger.error(f"Failed to load session: {e}")
            # Don't delete the session file immediately, might be temporary network issue
            return False

    def _attempt_session_login(self) -> bool:
        """Attempt to login using loaded session with robust error handling."""
        try:
            # Validate credentials first to prevent encode errors
            if not self._validate_credentials():
                logger.warning("Invalid credentials, cannot attempt session login")
                return False
            
            # First try to relogin with existing session
            try:
                success = self.client.relogin()
                if success:
                    # Verify the session works
                    if self._verify_session():
                        self.is_logged_in = True
                        logger.info("Session restored successfully via relogin")
                        return True
            except Exception as relogin_e:
                logger.debug(f"Relogin failed: {relogin_e}")
                    
            # If relogin fails, try fresh login
            logger.info("Session relogin failed, attempting fresh login")
            return self._fresh_login()
            
        except LoginRequired:
            logger.warning("Session expired, fresh login required")
            return self._fresh_login()
        except (ClientError, TwoFactorRequired) as e:
            logger.warning(f"Session login failed: {e}")
            return self._fresh_login()  # Try fresh login as fallback
        except Exception as e:
            error_msg = str(e)
            if "'NoneType' object has no attribute 'encode'" in error_msg:
                logger.error("Credentials encoding error - missing username/password")
                return self._handle_missing_credentials()
            else:
                logger.error(f"Unexpected session login error: {e}")
                return self._fresh_login()  # Try fresh login as fallback

    def _validate_credentials(self) -> bool:
        """Validate that credentials are properly set."""
        if not self.config_manager:
            logger.warning("No config manager available")
            return False
            
        username = getattr(self.config_manager, 'username', None)
        password = getattr(self.config_manager, 'password', None)
        
        if not username or not password:
            logger.warning(f"Missing credentials - username: {'✓' if username else '✗'}, password: {'✓' if password else '✗'}")
            return False
            
        if not isinstance(username, str) or not isinstance(password, str):
            logger.warning("Credentials are not strings")
            return False
            
        return True

    def _handle_missing_credentials(self) -> bool:
        """Handle missing credentials scenario."""
        logger.warning("Detected missing credentials, attempting to restore from config")
        
        try:
            # Try to reload config and restore credentials
            if self.config_manager:
                self.config_manager.load_config()
                
                # Check if credentials are now available
                if self._validate_credentials():
                    logger.info("Credentials restored from config")
                    return self._fresh_login()
                else:
                    logger.error("No valid credentials found in config - manual login required")
                    return False
        except Exception as e:
            logger.error(f"Failed to restore credentials: {e}")
            return False

    def _verify_session(self) -> bool:
        """Verify that the current session is working."""
        try:
            # Multiple verification methods to handle API issues
            
            # Method 1: Try to get user info
            try:
                if self.client.user_id:
                    user_info = self.client.account_info()
                    if user_info and hasattr(user_info, 'username'):
                        logger.info(f"Session verification successful via account_info: {user_info.username}")
                        return True
            except Exception as e:
                logger.debug(f"Account info verification failed: {e}")
            
            # Method 2: Try timeline feed
            try:
                timeline = self.client.get_timeline_feed()
                if timeline:
                    logger.info("Session verification successful via timeline")
                    return True
            except Exception as e:
                logger.debug(f"Timeline verification failed: {e}")
            
            # Method 3: Simple check if we have a user_id
            try:
                if hasattr(self.client, 'user_id') and self.client.user_id:
                    logger.info(f"Session verification successful - user_id: {self.client.user_id}")
                    return True
            except Exception as e:
                logger.debug(f"User ID verification failed: {e}")
            
            logger.warning("All session verification methods failed")
            return False
            
        except Exception as e:
            logger.warning(f"Session verification error: {e}")
            return False

    def _fresh_login(self) -> bool:
        """Perform a fresh login with credentials."""
        if not self._validate_credentials():
            logger.error("No valid credentials available for fresh login")
            return False
            
        try:
            username = self.config_manager.username
            password = self.config_manager.password
            logger.info(f"Attempting fresh login for user: {username}")
            return self.login(username, password)
        except Exception as e:
            logger.error(f"Fresh login attempt failed: {e}")
            return False

    def _backup_and_clear_session(self) -> None:
        """Backup corrupted session and clear it."""
        try:
            if os.path.exists(self.session_file):
                backup_file = f"{self.session_file}.backup.{int(time.time())}"
                os.rename(self.session_file, backup_file)
                logger.info(f"Corrupted session backed up to {backup_file}")
        except Exception as e:
            logger.warning(f"Could not backup session file: {e}")

    def login(self, username: str, password: str) -> bool:
        """Enhanced login with credentials and handle 2FA."""
        # Validate inputs
        if not username or not password:
            logger.error("Cannot login with empty username or password")
            return False
            
        if not isinstance(username, str) or not isinstance(password, str):
            logger.error("Username and password must be strings")
            return False
        
        
        try:
            # Configure client with unique device fingerprint
            self._configure_client_settings()
            
            # Attempt login with proper error handling
            logger.info(f"🔐 Attempting login for user: {username}")
            
            # Try login with additional error handling
            success = self.client.login(username, password)
            
            if success:
                # Verify the login actually worked
                if self.verify_login():
                    # Save session and credentials with device fingerprint
                    self._save_session_with_fingerprint()
                    self.set_credentials(username, password)
                    self.is_logged_in = True
                    logger.info(f"✅ Login successful for {username}. Session saved.")
                    return True
                else:
                    logger.error(f"❌ Login appeared successful but verification failed for {username}")
                    return False
            else:
                logger.error(f"❌ Login failed for {username} - no success response")
                return False
                
        except TwoFactorRequired as e:
            logger.warning("2FA required. Please handle manually.")
            # Don't mark as failed - user needs to handle 2FA
            return False
            
        except BadPassword as e:
            logger.error("Invalid username or password")
            self.is_logged_in = False
            return False
            
        except ChallengeRequired as e:
            logger.error("Challenge required - account may be flagged")
            self.is_logged_in = False
            return False
            
        except ClientError as e:
            error_msg = str(e).lower()
            if "rate limit" in error_msg:
                logger.error("Instagram rate limit exceeded")
            elif "login" in error_msg:
                logger.error("Login failed - check credentials")
            else:
                logger.error(f"Client error during login: {e}")
            self.is_logged_in = False
            return False
            
        except Exception as e:
            logger.error(f"Unexpected login error: {e}")
            self.is_logged_in = False
            return False

    def login_with_2fa(self, username: str, password: str, code: str) -> bool:
        """Enhanced login with 2FA code."""
        try:
            logger.info("Attempting 2FA login")
            success = self.client.login(username, password, verification_code=code)
            
            if success:
                self._save_session_with_fingerprint()
                self.set_credentials(username, password)
                self.is_logged_in = True
                logger.info("Login with 2FA successful.")
                return True
            else:
                logger.error("2FA login failed - no success response")
                return False
                
        except ClientError as e:
            logger.error(f"2FA login failed: {e}")
            self.is_logged_in = False
            return False
        except Exception as e:
            logger.error(f"Unexpected 2FA login error: {e}")
            self.is_logged_in = False
            return False

    def _save_session_with_fingerprint(self) -> None:
        """Save session along with device fingerprint for consistency across sessions."""
        try:
            # Get current settings from client
            current_settings = self.client.get_settings()
            
            # Add our device fingerprint and metadata to the settings
            if isinstance(current_settings, dict):
                current_settings['device_fingerprint'] = self.device_fingerprint
                current_settings['session_metadata'] = {
                    'saved_at': datetime.now().isoformat(),
                    'username': self.config_manager.username if self.config_manager else None,
                    'session_version': '2.0'  # Version for future compatibility
                }
            
            # Create backup of existing session file
            if os.path.exists(self.session_file):
                backup_file = f"{self.session_file}.backup"
                try:
                    import shutil
                    shutil.copy2(self.session_file, backup_file)
                except Exception:
                    pass  # Backup is optional
            
            # Save the enhanced settings atomically
            temp_file = f"{self.session_file}.tmp"
            with open(temp_file, 'w') as f:
                json.dump(current_settings, f, indent=2, ensure_ascii=False)
            
            # Atomic move to prevent corruption
            if os.path.exists(temp_file):
                os.replace(temp_file, self.session_file)
            
            logger.info(f"Session saved with device fingerprint to {self.session_file}")
            
        except Exception as e:
            logger.warning(f"Could not save session with fingerprint: {e}")
            # Fallback to standard session save
            try:
                self.client.dump_settings(self.session_file)
                logger.info("Session saved with standard method")
            except Exception as fallback_e:
                logger.error(f"Failed to save session entirely: {fallback_e}")
                
    def refresh_session(self) -> bool:
        """Refresh the current session to keep it active."""
        try:
            if not self.is_logged_in:
                return False
                
            # Perform a lightweight API call to refresh the session
            user_info = self.client.user_info(self.client.user_id)
            if user_info:
                # Save the refreshed session
                self._save_session_with_fingerprint()
                logger.info("Session refreshed successfully")
                return True
            return False
            
        except Exception as e:
            logger.warning(f"Session refresh failed: {e}")
            return False

    def verify_login(self) -> bool:
        """Enhanced login verification with robust error handling."""
        try:
            # First check if we have basic session info
            if not hasattr(self.client, 'user_id') or not self.client.user_id:
                logger.debug("No user_id found - not logged in")
                self.is_logged_in = False
                return False
            
            # Method 1: Try account info (most reliable)
            try:
                account_info = self.client.account_info()
                if account_info and hasattr(account_info, 'username'):
                    logger.info(f"✅ Login verified via account info: {account_info.username}")
                    self.is_logged_in = True
                    return True
            except Exception as e:
                logger.debug(f"Account info verification failed: {e}")
            
            # Method 2: Check if user_id is valid without making API calls
            try:
                if self.client.user_id and str(self.client.user_id).isdigit():
                    # We have a valid user_id, assume logged in
                    logger.info(f"✅ Login verified via user_id: {self.client.user_id}")
                    self.is_logged_in = True
                    return True
            except Exception as e:
                logger.debug(f"User ID check failed: {e}")
            
            # Method 3: Try a simple API call
            try:
                # Use a lightweight API call to test login
                self.client.get_timeline_feed(amount=1)  # Just get 1 item
                logger.info("✅ Login verified via timeline")
                self.is_logged_in = True
                return True
            except Exception as e:
                logger.debug(f"Timeline verification failed: {e}")
            
            logger.warning("❌ All login verification methods failed")
            self.is_logged_in = False
            return False
            
        except Exception as e:
            logger.error(f"Login verification error: {e}")
            self.is_logged_in = False
            return False

    def logout(self) -> None:
        """Enhanced logout and session cleanup."""
        try:
            if self.is_logged_in:
                self.client.logout()
                logger.info("Logged out successfully.")
            
            # Clear login state
            self.is_logged_in = False
            self.username = ''
            self.password = ''
            
        except Exception as e:
            logger.warning(f"Logout error: {e}")
            # Still mark as logged out even if logout call failed
            self.is_logged_in = False

    def save_session(self, filepath: str = None) -> bool:
        """Save current session to file."""
        try:
            save_path = filepath or self.session_file
            
            # Use enhanced save method if saving to default session file
            if save_path == self.session_file:
                self._save_session_with_fingerprint()
                return True
            else:
                # For custom paths, use standard method
                self.client.dump_settings(save_path)
                logger.info(f"Session saved to {save_path}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to save session: {e}")
            return False

    def set_proxy(self, proxy: Optional[str]) -> None:
        """Set proxy for the client."""
        try:
            if proxy:
                self.client.set_proxy(proxy)
                logger.info(f"Proxy set: {proxy}")
            else:
                logger.info("Proxy cleared")
        except Exception as e:
            logger.error(f"Failed to set proxy: {e}")

    def get_account_type(self) -> str:
        """Get the type of Instagram account (Personal, Business, Creator)."""
        try:
            if not self.is_logged_in:
                return "Unknown - Not logged in"
                
            account_info = self.client.account_info()
            if hasattr(account_info, 'account_type'):
                account_type = account_info.account_type
                if account_type == 1:
                    return "Personal"
                elif account_type == 2:
                    return "Business"
                elif account_type == 3:
                    return "Creator"
                else:
                    return f"Unknown ({account_type})"
            return "Unknown - No account type info"
            
        except Exception as e:
            logger.error(f"Failed to get account type: {e}")
            return "Unknown - Error"

    def diagnose_connection(self) -> dict:
        """Comprehensive connection diagnostics."""
        diagnostics = {
            'is_logged_in': self.is_logged_in,
            'session_file_exists': os.path.exists(self.session_file),
            'session_file_valid': False,
            'account_type': 'Unknown',
            'api_accessible': False,
            'recommendations': []
        }
        
        # Check session file
        if diagnostics['session_file_exists']:
            try:
                with open(self.session_file, 'r') as f:
                    session_data = json.load(f)
                    if session_data and isinstance(session_data, dict):
                        diagnostics['session_file_valid'] = True
            except:
                diagnostics['recommendations'].append("Session file is corrupted - clear and re-login")
        
        # Check API accessibility
        try:
            if self.is_logged_in:
                timeline = self.client.get_timeline_feed()
                if timeline:
                    diagnostics['api_accessible'] = True
                    diagnostics['account_type'] = self.get_account_type()
                    
                    # Check if business account
                    if diagnostics['account_type'] == 'Personal':
                        diagnostics['recommendations'].append("Consider switching to Business or Creator account for better API access")
                        
        except Exception as e:
            diagnostics['recommendations'].append(f"API access test failed: {str(e)}")
            
        # Provide specific recommendations
        if not diagnostics['is_logged_in']:
            diagnostics['recommendations'].append("Login required")
        elif not diagnostics['api_accessible']:
            diagnostics['recommendations'].append("API not accessible - session may be expired")
            
        return diagnostics

    def clear_session(self) -> bool:
        """Clear session file and logout."""
        try:
            self.logout()
            
            if os.path.exists(self.session_file):
                self._backup_and_clear_session()
                
            logger.info("Session cleared successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear session: {e}")
            return False
