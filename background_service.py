"""
Background Service for Multi-Account Instagram Autoposter
Handles scheduling, automation, and background operations
"""
import logging
import threading
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import schedule

from account_manager import AccountManager, AccountInstance
from caption_generator import CaptionGenerator

logger = logging.getLogger(__name__)


@dataclass
class PostingJob:
    """Represents a scheduled posting job."""
    account_id: str
    video_path: str
    caption: str
    scheduled_time: datetime
    job_id: str
    status: str = "pending"  # pending, running, completed, failed
    created_at: datetime = None
    completed_at: datetime = None
    error_message: str = ""
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class BackgroundService:
    """Background service for managing multi-account posting operations."""
    
    def __init__(self, account_manager: AccountManager, caption_generator: CaptionGenerator):
        self.account_manager = account_manager
        self.caption_generator = caption_generator
        
        # Service state
        self.is_running = False
        self.service_thread = None
        self._stop_event = threading.Event()
        
        # Scheduling
        self.scheduled_jobs: Dict[str, PostingJob] = {}
        self.job_queue = []
        self.jobs_file = "scheduled_jobs.json"
        
        # Status tracking
        self.status_callbacks: List[Callable] = []
        self.last_status_update = datetime.now()
        
        # Load existing jobs
        self.load_scheduled_jobs()
        
        # Performance monitoring
        self.performance_stats = {
            "jobs_completed": 0,
            "jobs_failed": 0,
            "total_posts": 0,
            "service_uptime": 0,
            "last_restart": datetime.now()
        }

    def start_service(self) -> bool:
        """Start the background service."""
        if self.is_running:
            logger.warning("Background service is already running")
            return False
        
        try:
            self.is_running = True
            self._stop_event.clear()
            
            # Start the main service thread
            self.service_thread = threading.Thread(target=self._service_loop, daemon=True)
            self.service_thread.start()
            
            logger.info("Background service started successfully")
            self._notify_status_change("started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start background service: {e}")
            self.is_running = False
            return False

    def stop_service(self) -> bool:
        """Stop the background service."""
        if not self.is_running:
            logger.warning("Background service is not running")
            return False
        
        try:
            self.is_running = False
            self._stop_event.set()
            
            # Wait for service thread to finish
            if self.service_thread and self.service_thread.is_alive():
                self.service_thread.join(timeout=10)
            
            # Save current state
            self.save_scheduled_jobs()
            
            logger.info("Background service stopped successfully")
            self._notify_status_change("stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop background service: {e}")
            return False

    def _service_loop(self):
        """Main service loop."""
        logger.info("Background service loop started")
        
        while self.is_running and not self._stop_event.is_set():
            try:
                # Process scheduled jobs
                self._process_scheduled_jobs()
                
                # Check auto-posting accounts
                self._check_auto_posting()
                
                # Update performance stats
                self._update_performance_stats()
                
                # Notify status updates
                self._check_status_updates()
                
                # Sleep for a short interval
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in service loop: {e}")
                time.sleep(10)  # Longer sleep on error
        
        logger.info("Background service loop ended")

    def _process_scheduled_jobs(self):
        """Process scheduled posting jobs."""
        now = datetime.now()
        
        for job_id, job in list(self.scheduled_jobs.items()):
            if job.status != "pending":
                continue
                
            # Check if job is due
            if job.scheduled_time <= now:
                self._execute_posting_job(job)

    def _execute_posting_job(self, job: PostingJob):
        """Execute a single posting job."""
        logger.info(f"Executing posting job {job.job_id} for account {job.account_id}")
        
        try:
            job.status = "running"
            
            # Get account instance
            account = self.account_manager.get_account(job.account_id)
            if not account:
                raise Exception(f"Account {job.account_id} not found")
            
            # Verify account is logged in
            if not account.verify_login():
                raise Exception(f"Account {job.account_id} is not logged in")
            
            # Check rate limiting
            can_post, remaining = account.posting_manager.check_rate_limit()
            if not can_post:
                raise Exception("Rate limit exceeded for this account")
            
            # Validate video file
            if not os.path.exists(job.video_path):
                raise Exception(f"Video file not found: {job.video_path}")
            
            # Create posting item
            posting_item = {
                'file': job.video_path,
                'caption': job.caption,
                'status': 'pending',
                'is_scheduled': True,
                'job_id': job.job_id
            }
            
            # Post the video
            def posting_callback(level, message):
                logger.info(f"Job {job.job_id}: {message}")
            
            account.posting_manager.post_reel(
                posting_item, 
                dry_run=False,  # Scheduled jobs are real posts
                callback=posting_callback
            )
            
            # Wait for completion (with timeout)
            timeout = 300  # 5 minutes timeout
            start_time = time.time()
            
            while posting_item['status'] == 'pending' and (time.time() - start_time) < timeout:
                time.sleep(2)
            
            if posting_item['status'] == 'posted':
                job.status = "completed"
                job.completed_at = datetime.now()
                self.performance_stats["jobs_completed"] += 1
                self.performance_stats["total_posts"] += 1
                logger.info(f"Job {job.job_id} completed successfully")
            else:
                raise Exception(f"Posting failed with status: {posting_item['status']}")
                
        except Exception as e:
            job.status = "failed"
            job.error_message = str(e)
            job.completed_at = datetime.now()
            self.performance_stats["jobs_failed"] += 1
            logger.error(f"Job {job.job_id} failed: {e}")
        
        # Save updated job state
        self.save_scheduled_jobs()
        self._notify_job_update(job)

    def _check_auto_posting(self):
        """Check accounts with auto-posting enabled."""
        for account_id, account in self.account_manager.get_all_accounts().items():
            if not account.config.is_active or not account.config.auto_post:
                continue
                
            if not account.verify_login():
                continue
            
            # Check if it's time to post based on schedule
            if self._should_auto_post(account):
                self._trigger_auto_post(account)

    def _should_auto_post(self, account: AccountInstance) -> bool:
        """Determine if an account should auto-post now."""
        schedule_type = account.config.post_schedule
        
        if schedule_type == "manual":
            return False
        
        # Get the last post time for this account
        stats = account.posting_manager.get_posting_stats()
        rate_info = account.posting_manager.get_rate_limit_info()
        
        # Check rate limits
        if not rate_info['can_post']:
            return False
        
        # Check if there are pending items in queue
        pending_count = stats.get('pending', 0)
        if pending_count == 0:
            return False
        
        # Check schedule
        now = datetime.now()
        
        if schedule_type == "hourly":
            # Post every hour if there's content
            last_post_time = rate_info.get('last_post_time')
            if last_post_time:
                last_post = datetime.fromisoformat(last_post_time)
                if (now - last_post).total_seconds() < 3600:  # 1 hour
                    return False
            return True
            
        elif schedule_type == "daily":
            # Post once per day at a specific time
            if now.hour == 9:  # 9 AM
                last_post_time = rate_info.get('last_post_time')
                if last_post_time:
                    last_post = datetime.fromisoformat(last_post_time)
                    if last_post.date() == now.date():
                        return False
                return True
            return False
            
        elif schedule_type == "custom":
            # Use custom cron-like schedule
            # TODO: Implement cron parsing
            return False
        
        return False

    def _trigger_auto_post(self, account: AccountInstance):
        """Trigger an auto-post for an account."""
        try:
            # Get next pending item from queue
            next_item = account.queue_manager.get_next_pending()
            if not next_item:
                return
            
            logger.info(f"Auto-posting for account {account.account_id}")
            
            # Generate caption if needed
            if account.config.caption_mode.startswith("ai_"):
                style = account.config.ai_caption_style
                caption_result = self.caption_generator.generate_caption(
                    next_item['file'], style
                )
                if caption_result['success']:
                    next_item['caption'] = caption_result['caption']
            
            # Post the item
            def posting_callback(level, message):
                logger.info(f"Auto-post {account.account_id}: {message}")
            
            account.posting_manager.post_reel(
                next_item, 
                dry_run=False,
                callback=posting_callback
            )
            
        except Exception as e:
            logger.error(f"Auto-posting failed for {account.account_id}: {e}")

    def schedule_post(self, account_id: str, video_path: str, caption: str, 
                     scheduled_time: datetime) -> Optional[str]:
        """Schedule a post for later."""
        try:
            # Validate inputs
            if not self.account_manager.get_account(account_id):
                raise ValueError(f"Account {account_id} not found")
                
            if not os.path.exists(video_path):
                raise ValueError(f"Video file not found: {video_path}")
                
            if scheduled_time <= datetime.now():
                raise ValueError("Scheduled time must be in the future")
            
            # Create job
            job_id = f"{account_id}_{int(time.time())}_{len(self.scheduled_jobs)}"
            job = PostingJob(
                account_id=account_id,
                video_path=video_path,
                caption=caption,
                scheduled_time=scheduled_time,
                job_id=job_id
            )
            
            # Add to scheduled jobs
            self.scheduled_jobs[job_id] = job
            self.save_scheduled_jobs()
            
            logger.info(f"Scheduled post for {account_id} at {scheduled_time}")
            self._notify_job_update(job)
            
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to schedule post: {e}")
            return None

    def cancel_scheduled_post(self, job_id: str) -> bool:
        """Cancel a scheduled post."""
        try:
            if job_id in self.scheduled_jobs:
                job = self.scheduled_jobs[job_id]
                if job.status == "pending":
                    job.status = "cancelled"
                    self.save_scheduled_jobs()
                    logger.info(f"Cancelled scheduled job {job_id}")
                    self._notify_job_update(job)
                    return True
                else:
                    logger.warning(f"Cannot cancel job {job_id} with status {job.status}")
            else:
                logger.error(f"Job {job_id} not found")
            return False
        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {e}")
            return False

    def get_scheduled_jobs(self, account_id: Optional[str] = None) -> List[PostingJob]:
        """Get scheduled jobs, optionally filtered by account."""
        jobs = list(self.scheduled_jobs.values())
        
        if account_id:
            jobs = [job for job in jobs if job.account_id == account_id]
        
        # Sort by scheduled time
        jobs.sort(key=lambda j: j.scheduled_time)
        return jobs

    def get_service_status(self) -> Dict[str, Any]:
        """Get comprehensive service status."""
        now = datetime.now()
        uptime = (now - self.performance_stats["last_restart"]).total_seconds()
        
        pending_jobs = len([j for j in self.scheduled_jobs.values() if j.status == "pending"])
        running_jobs = len([j for j in self.scheduled_jobs.values() if j.status == "running"])
        
        return {
            "is_running": self.is_running,
            "uptime_seconds": uptime,
            "uptime_formatted": str(timedelta(seconds=int(uptime))),
            "total_scheduled_jobs": len(self.scheduled_jobs),
            "pending_jobs": pending_jobs,
            "running_jobs": running_jobs,
            "completed_jobs": self.performance_stats["jobs_completed"],
            "failed_jobs": self.performance_stats["jobs_failed"],
            "total_posts": self.performance_stats["total_posts"],
            "last_activity": self.last_status_update.isoformat(),
            "auto_post_accounts": sum(1 for acc in self.account_manager.get_all_accounts().values() 
                                     if acc.config.auto_post and acc.config.is_active)
        }

    def add_status_callback(self, callback: Callable):
        """Add a callback for status updates."""
        self.status_callbacks.append(callback)

    def remove_status_callback(self, callback: Callable):
        """Remove a status callback."""
        if callback in self.status_callbacks:
            self.status_callbacks.remove(callback)

    def _notify_status_change(self, event_type: str):
        """Notify callbacks of status changes."""
        for callback in self.status_callbacks:
            try:
                callback("status", event_type, self.get_service_status())
            except Exception as e:
                logger.error(f"Error in status callback: {e}")

    def _notify_job_update(self, job: PostingJob):
        """Notify callbacks of job updates."""
        for callback in self.status_callbacks:
            try:
                callback("job", job.status, {
                    "job_id": job.job_id,
                    "account_id": job.account_id,
                    "status": job.status,
                    "scheduled_time": job.scheduled_time.isoformat(),
                    "error_message": job.error_message
                })
            except Exception as e:
                logger.error(f"Error in job callback: {e}")

    def _check_status_updates(self):
        """Check if status updates should be sent."""
        now = datetime.now()
        if (now - self.last_status_update).total_seconds() >= 30:  # Every 30 seconds
            self._notify_status_change("periodic_update")
            self.last_status_update = now

    def _update_performance_stats(self):
        """Update performance statistics."""
        # Clean up completed jobs older than 24 hours
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        jobs_to_remove = []
        for job_id, job in self.scheduled_jobs.items():
            if job.status in ["completed", "failed", "cancelled"] and job.completed_at:
                if job.completed_at < cutoff_time:
                    jobs_to_remove.append(job_id)
        
        for job_id in jobs_to_remove:
            del self.scheduled_jobs[job_id]
        
        if jobs_to_remove:
            self.save_scheduled_jobs()

    def load_scheduled_jobs(self):
        """Load scheduled jobs from file."""
        try:
            if os.path.exists(self.jobs_file):
                with open(self.jobs_file, 'r') as f:
                    jobs_data = json.load(f)
                
                self.scheduled_jobs = {}
                for job_data in jobs_data.get('jobs', []):
                    job = PostingJob(
                        account_id=job_data['account_id'],
                        video_path=job_data['video_path'],
                        caption=job_data['caption'],
                        scheduled_time=datetime.fromisoformat(job_data['scheduled_time']),
                        job_id=job_data['job_id'],
                        status=job_data.get('status', 'pending'),
                        created_at=datetime.fromisoformat(job_data.get('created_at', datetime.now().isoformat())),
                        completed_at=datetime.fromisoformat(job_data['completed_at']) if job_data.get('completed_at') else None,
                        error_message=job_data.get('error_message', '')
                    )
                    self.scheduled_jobs[job.job_id] = job
                
                logger.info(f"Loaded {len(self.scheduled_jobs)} scheduled jobs")
                
        except Exception as e:
            logger.error(f"Failed to load scheduled jobs: {e}")
            self.scheduled_jobs = {}

    def save_scheduled_jobs(self):
        """Save scheduled jobs to file."""
        try:
            jobs_data = {
                'jobs': [],
                'last_updated': datetime.now().isoformat()
            }
            
            for job in self.scheduled_jobs.values():
                job_data = {
                    'account_id': job.account_id,
                    'video_path': job.video_path,
                    'caption': job.caption,
                    'scheduled_time': job.scheduled_time.isoformat(),
                    'job_id': job.job_id,
                    'status': job.status,
                    'created_at': job.created_at.isoformat() if job.created_at else None,
                    'completed_at': job.completed_at.isoformat() if job.completed_at else None,
                    'error_message': job.error_message
                }
                jobs_data['jobs'].append(job_data)
            
            with open(self.jobs_file, 'w') as f:
                json.dump(jobs_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save scheduled jobs: {e}")

    def cleanup_old_jobs(self, days: int = 7):
        """Clean up old completed/failed jobs."""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        jobs_to_remove = []
        for job_id, job in self.scheduled_jobs.items():
            if job.status in ["completed", "failed", "cancelled"] and job.completed_at:
                if job.completed_at < cutoff_time:
                    jobs_to_remove.append(job_id)
        
        for job_id in jobs_to_remove:
            del self.scheduled_jobs[job_id]
        
        if jobs_to_remove:
            self.save_scheduled_jobs()
            logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")
        
        return len(jobs_to_remove)

    def bulk_schedule_posts(self, account_id: str, video_folder: str, 
                           start_time: datetime, interval_hours: int = 1) -> List[str]:
        """Schedule multiple posts from a folder with intervals."""
        try:
            import glob
            
            # Find video files
            video_extensions = ['*.mp4', '*.mov']
            video_files = []
            
            for ext in video_extensions:
                video_files.extend(glob.glob(os.path.join(video_folder, ext)))
            
            if not video_files:
                logger.warning(f"No video files found in {video_folder}")
                return []
            
            # Schedule posts
            job_ids = []
            current_time = start_time
            
            for video_file in video_files[:50]:  # Limit to 50 files to respect rate limits
                # Generate caption if AI is enabled
                account = self.account_manager.get_account(account_id)
                if account and account.config.caption_mode.startswith("ai_"):
                    style = account.config.ai_caption_style
                    caption_result = self.caption_generator.generate_caption(
                        video_file, style
                    )
                    caption = caption_result['caption'] if caption_result['success'] else f"New post! {os.path.basename(video_file)}"
                else:
                    caption = f"New post! {os.path.basename(video_file)}"
                
                # Schedule the post
                job_id = self.schedule_post(account_id, video_file, caption, current_time)
                if job_id:
                    job_ids.append(job_id)
                
                # Increment time
                current_time += timedelta(hours=interval_hours)
            
            logger.info(f"Bulk scheduled {len(job_ids)} posts for account {account_id}")
            return job_ids
            
        except Exception as e:
            logger.error(f"Bulk scheduling failed: {e}")
            return []
