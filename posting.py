"""
Enhanced Posting functionality for Instagram Reels Autoposter
"""
import logging
import threading
import time
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, Callable, Tuple
from instagrapi import Client
from instagrapi.exceptions import RateLimitError, ClientError, VideoTooLongException

logger = logging.getLogger(__name__)


class PostingManager:
    """Enhanced Instagram posting manager with content validation and rate limiting."""

    def __init__(self, client: Client, config_manager, queue_manager, account_id: str = None):
        self.client = client
        self.config = config_manager
        self.queue = queue_manager
        self.account_id = account_id or "default"
        self._posting_active = False
        
        # Rate limiting configuration (Instagram allows 50 posts per 24 hours)
        # Make rate limit file account-specific to avoid conflicts
        account_folder = Path(f"accounts/{self.account_id}")
        account_folder.mkdir(parents=True, exist_ok=True)
        self.rate_limit_file = str(account_folder / 'posting_history.json')
        self.max_posts_per_day = 50
        self.posting_history = []
        self.load_posting_history()
        
        # Content validation settings
        self.supported_video_formats = {'.mp4', '.mov'}
        self.max_video_size_mb = 4000  # 4GB limit
        self.max_video_duration = 90   # 90 seconds for reels
        self.min_video_duration = 3    # 3 seconds minimum
        self.recommended_aspect_ratio = 9/16  # 9:16 for vertical videos

    def load_posting_history(self) -> None:
        """Load posting history for rate limiting."""
        try:
            if os.path.exists(self.rate_limit_file):
                with open(self.rate_limit_file, 'r') as f:
                    data = json.load(f)
                    self.posting_history = data.get('posts', [])
                    # Clean old entries (older than 24 hours)
                    self.clean_posting_history()
        except Exception as e:
            logger.warning(f"Could not load posting history: {e}")
            self.posting_history = []

    def save_posting_history(self) -> None:
        """Save posting history to file."""
        try:
            data = {
                'posts': self.posting_history,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.rate_limit_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Could not save posting history: {e}")

    def clean_posting_history(self) -> None:
        """Remove posting history entries older than 24 hours."""
        cutoff_time = datetime.now() - timedelta(hours=24)
        original_count = len(self.posting_history)
        
        self.posting_history = [
            post for post in self.posting_history
            if datetime.fromisoformat(post['timestamp']) > cutoff_time
        ]
        
        cleaned_count = original_count - len(self.posting_history)
        if cleaned_count > 0:
            logger.info(f"Cleaned {cleaned_count} old posting history entries")

    def check_rate_limit(self) -> Tuple[bool, int]:
        """Check if we're within Instagram's rate limits.
        
        Returns:
            Tuple of (can_post, remaining_posts)
        """
        self.clean_posting_history()
        current_posts_today = len(self.posting_history)
        remaining_posts = self.max_posts_per_day - current_posts_today
        
        can_post = current_posts_today < self.max_posts_per_day
        return can_post, remaining_posts

    def record_post(self, post_id: str = None) -> None:
        """Record a successful post for rate limiting."""
        post_record = {
            'timestamp': datetime.now().isoformat(),
            'post_id': post_id or 'unknown'
        }
        self.posting_history.append(post_record)
        self.save_posting_history()
        
        can_post, remaining = self.check_rate_limit()
        logger.info(f"Post recorded. Remaining posts today: {remaining}")

    def validate_video_advanced(self, video_path: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Advanced video validation with detailed analysis.
        
        Returns:
            Tuple of (is_valid, error_message, metadata)
        """
        path = Path(video_path)
        metadata = {
            'file_size_mb': 0,
            'duration_seconds': 0,
            'format': '',
            'resolution': None,
            'aspect_ratio': None,
            'has_audio': False
        }
        
        # Check file existence
        if not path.exists():
            return False, "File does not exist", metadata
            
        if not path.is_file():
            return False, "Path is not a file", metadata
            
        # Check file format
        file_extension = path.suffix.lower()
        if file_extension not in self.supported_video_formats:
            return False, f"Unsupported format: {file_extension}. Supported: {', '.join(self.supported_video_formats)}", metadata
            
        metadata['format'] = file_extension
        
        # Check file size
        size_bytes = path.stat().st_size
        size_mb = size_bytes / (1024 * 1024)
        metadata['file_size_mb'] = round(size_mb, 2)
        
        if size_mb > self.max_video_size_mb:
            return False, f"File too large: {size_mb:.1f}MB (max: {self.max_video_size_mb}MB)", metadata
            
        # Try to get video metadata using basic methods
        try:
            # This is a simplified check - in production you'd use ffprobe or similar
            # For now, we'll do basic validation
            if size_mb < 0.1:  # Less than 100KB is likely too small
                return False, "File appears to be too small for a valid video", metadata
                
        except Exception as e:
            logger.warning(f"Could not analyze video metadata: {e}")
            
        return True, "Valid", metadata

    def post_reel(self, item: Dict[str, Any], dry_run: bool = False,
                  location: Optional[str] = None, callback: Optional[Callable] = None) -> None:
        """Enhanced reel posting with validation, rate limiting, and modern error handling."""
        def _post_thread():
            retries = 0
            max_retries = self.config.max_retries
            retry_backoff = self.config.retry_backoff

            # Check rate limiting first
            can_post, remaining_posts = self.check_rate_limit()
            if not can_post:
                error_msg = f"Rate limit exceeded. 0 posts remaining in 24-hour window. Try again later."
                logger.error(error_msg)
                if callback:
                    callback("error", error_msg)
                item['status'] = 'rate_limited'
                self.queue.save_queue()
                return

            if callback:
                callback("info", f"Rate limit check passed. {remaining_posts} posts remaining today.")

            file_path = item['file']
            caption = item['caption']

            # Enhanced video validation
            is_valid, validation_msg, metadata = self.validate_video_advanced(file_path)
            if not is_valid:
                error_msg = f"Video validation failed: {validation_msg}"
                logger.error(error_msg)
                if callback:
                    callback("error", error_msg)
                item['status'] = 'failed'
                item['validation_error'] = validation_msg
                item['metadata'] = metadata
                self.queue.save_queue()
                return

            # Log video metadata
            logger.info(f"Video validated: {metadata['file_size_mb']}MB, {metadata['format']}")
            if callback:
                callback("info", f"Video validated: {metadata['file_size_mb']}MB {metadata['format']}")

            while retries < max_retries:
                try:
                    if dry_run:
                        logger.info(f"DRY RUN: Would post {Path(item['file']).name}")
                        item['status'] = 'posted'
                        # Don't record in posting history for dry runs
                        if callback:
                            callback("success", f"DRY RUN: Posted {Path(item['file']).name}")
                        return

                    # Try posting with Reels-specific method first
                    try:
                        # Use clip_upload for Reels (more reliable for short videos)
                        media = self.client.clip_upload(file_path, caption, location=location)
                        
                        # Record successful post
                        item['status'] = 'posted'
                        item['post_id'] = media.id
                        item['posted_at'] = time.time()
                        item['metadata'] = metadata

                        # Update rate limiting
                        self.record_post(media.id)

                        logger.info(f"Posted reel {Path(file_path).name} - ID: {media.id}")
                        if callback:
                            callback("success", f"Posted reel {Path(file_path).name} - ID: {media.id}")

                        self.queue.save_queue()
                        return

                    except Exception as upload_error:
                        error_str = str(upload_error).lower()
                        
                        # Try fallback to regular video upload
                        if "clip" in error_str or "reel" in error_str:
                            logger.warning("Clip upload failed, trying regular video upload")
                            if callback:
                                callback("warning", "Reel upload failed, trying regular video upload...")
                                
                            try:
                                media = self.client.video_upload(file_path, caption, location=location)
                                
                                # Record successful post
                                item['status'] = 'posted'
                                item['post_id'] = media.id
                                item['posted_at'] = time.time()
                                item['metadata'] = metadata

                                # Update rate limiting
                                self.record_post(media.id)

                                logger.info(f"Posted video {Path(file_path).name} - ID: {media.id}")
                                if callback:
                                    callback("success", f"Posted video {Path(file_path).name} - ID: {media.id}")

                                self.queue.save_queue()
                                return
                                
                            except Exception as video_error:
                                logger.error(f"Both clip and video upload failed: {video_error}")
                                raise video_error
                        
                        # Handle API format changes
                        elif "data" in error_str and "keyerror" in error_str:
                            logger.warning("Instagram API response format changed")
                            if callback:
                                callback("warning", "Instagram API changed. Please update instagrapi and try logging in again.")
                            raise upload_error
                            
                        # Handle session issues
                        elif "login" in error_str or "session" in error_str:
                            logger.error("Session expired during upload")
                            if callback:
                                callback("error", "Session expired. Please login again.")
                            raise upload_error
                            
                        else:
                            raise upload_error

                except RateLimitError as e:
                    wait_time = retry_backoff * (2 ** retries)
                    logger.warning(f"Instagram rate limit hit. Waiting {wait_time}s... (attempt {retries + 1}/{max_retries})")
                    if callback:
                        callback("warning", f"Rate limit hit. Waiting {wait_time}s... (attempt {retries + 1}/{max_retries})")
                    time.sleep(wait_time)
                    retries += 1

                except VideoTooLongException as e:
                    logger.error(f"Video too long for Instagram: {e}")
                    if callback:
                        callback("error", f"Video too long for Instagram (max {self.max_video_duration}s)")
                    item['status'] = 'failed'
                    item['error'] = 'Video too long'
                    self.queue.save_queue()
                    return

                except ClientError as e:
                    error_str = str(e).lower()
                    retries += 1

                    # Handle specific client errors with detailed messages
                    if "login" in error_str or "session" in error_str:
                        logger.error("Session expired or invalid. Please login again.")
                        if callback:
                            callback("error", "Session expired. Please login again in the Authentication tab.")
                        item['status'] = 'session_expired'
                        self.queue.save_queue()
                        return
                    elif "challenge" in error_str:
                        logger.error("Instagram challenge required - account may be flagged")
                        if callback:
                            callback("error", "Instagram challenge required. Please resolve manually.")
                        item['status'] = 'challenge_required'
                        self.queue.save_queue()
                        return
                    else:
                        logger.error(f"Client error: {e}")
                        if callback:
                            callback("error", f"Instagram client error: {e}")

                    if retries < max_retries:
                        time.sleep(retry_backoff)

                except Exception as e:
                    error_str = str(e).lower()
                    retries += 1

                    # Handle specific exceptions with actionable advice
                    if "pydantic" in error_str or "validation" in error_str:
                        logger.error("Instagram API validation error. Library may need updating.")
                        if callback:
                            callback("error", "Instagram API changed. Update instagrapi: pip install --upgrade instagrapi")
                    elif "network" in error_str or "connection" in error_str:
                        logger.error("Network error. Check your internet connection.")
                        if callback:
                            callback("error", "Network error. Check your internet connection and try again.")
                    elif "forbidden" in error_str or "403" in error_str:
                        logger.error("Instagram blocked the request - may need to wait or use different content")
                        if callback:
                            callback("error", "Request blocked by Instagram. Try different content or wait.")
                    else:
                        logger.error(f"Unexpected post error: {e}")
                        if callback:
                            callback("error", f"Unexpected error: {e}")

                    if retries < max_retries:
                        time.sleep(retry_backoff)

            # Final failure handling
            logger.error(f"Failed to post after {max_retries} retries.")
            if callback:
                callback("error", f"Failed after {max_retries} retries. Check connection, update instagrapi, or verify account status.")
            item['status'] = 'failed'
            item['final_error'] = f"Failed after {max_retries} retries"
            self.queue.save_queue()

        thread = threading.Thread(target=_post_thread, daemon=True)
        thread.start()

    def post_test_video(self, video_path: str, caption: str = "Test post #reels #test",
                       location: Optional[str] = None, callback: Optional[Callable] = None) -> None:
        """Post a test video without affecting the main queue."""
        if not Path(video_path).exists():
            error_msg = f"Test video file not found: {video_path}"
            logger.error(error_msg)
            if callback:
                callback("error", error_msg)
            return

        # Check rate limiting first
        can_post, remaining_posts = self.check_rate_limit()
        if not can_post:
            error_msg = f"Rate limit exceeded. Cannot post test video. {remaining_posts} posts remaining today."
            logger.error(error_msg)
            if callback:
                callback("error", error_msg)
            return

        # Enhanced video validation
        is_valid, validation_msg, metadata = self.validate_video_advanced(video_path)
        if not is_valid:
            error_msg = f"Test video validation failed: {validation_msg}"
            logger.error(error_msg)
            if callback:
                callback("error", error_msg)
            return

        test_item = {
            'file': video_path,
            'caption': caption,
            'status': 'pending',
            'is_test': True,
            'metadata': metadata
        }

        logger.info(f"Posting test video: {Path(video_path).name} ({metadata['file_size_mb']}MB)")
        if callback:
            callback("info", f"Posting test video: {Path(video_path).name} ({metadata['file_size_mb']}MB)")

        # Check if we're logged in before attempting to post
        try:
            if not hasattr(self.client, 'user_id') or not self.client.user_id:
                error_msg = "Not logged in. Please login first in the Authentication tab."
                logger.error(error_msg)
                if callback:
                    callback("error", error_msg)
                return
        except Exception:
            error_msg = "Login session invalid. Please login again."
            logger.error(error_msg)
            if callback:
                callback("error", error_msg)
            return

        self.post_reel(test_item, dry_run=self.config.dry_run, location=location, callback=callback)

    def post_single_video(self, video_path: str, caption: Optional[str] = None,
                         location: Optional[str] = None, callback: Optional[Callable] = None) -> None:
        """Post a single video instantly with enhanced validation."""
        if not Path(video_path).exists():
            error_msg = f"Video file not found: {video_path}"
            logger.error(error_msg)
            if callback:
                callback("error", error_msg)
            return

        # Check rate limiting first
        can_post, remaining_posts = self.check_rate_limit()
        if not can_post:
            error_msg = f"Rate limit exceeded. Cannot post video. {remaining_posts} posts remaining today."
            logger.error(error_msg)
            if callback:
                callback("error", error_msg)
            return

        # Enhanced video validation
        is_valid, validation_msg, metadata = self.validate_video_advanced(video_path)
        if not is_valid:
            error_msg = f"Video validation failed: {validation_msg}"
            logger.error(error_msg)
            if callback:
                callback("error", error_msg)
            return

        if caption is None:
            caption = f"{Path(video_path).stem} #reels #instagram"

        single_item = {
            'file': video_path,
            'caption': caption,
            'status': 'pending',
            'is_single': True,
            'metadata': metadata
        }

        logger.info(f"Posting single video: {Path(video_path).name} ({metadata['file_size_mb']}MB)")
        if callback:
            callback("info", f"Posting single video: {Path(video_path).name} ({metadata['file_size_mb']}MB)")

        # Check if we're logged in before attempting to post
        try:
            if not hasattr(self.client, 'user_id') or not self.client.user_id:
                error_msg = "Not logged in. Please login first in the Authentication tab."
                logger.error(error_msg)
                if callback:
                    callback("error", error_msg)
                return
        except Exception:
            error_msg = "Login session invalid. Please login again."
            logger.error(error_msg)
            if callback:
                callback("error", error_msg)
            return

        self.post_reel(single_item, dry_run=self.config.dry_run, location=location, callback=callback)

    def post_batch(self, items: list, dry_run: bool = False, location: Optional[str] = None,
                   callback: Optional[Callable] = None, progress_callback: Optional[Callable] = None) -> None:
        """Post multiple items with batch delay."""
        batch_delay = self.config.batch_delay

        for i, item in enumerate(items):
            if item['status'] == 'pending' and item.get('selected', True):
                self.post_reel(item, dry_run, location, callback)

                # Progress callback
                if progress_callback:
                    progress_callback(i + 1, len(items))

                # Batch delay between posts
                if i < len(items) - 1:  # Don't delay after last item
                    time.sleep(batch_delay)

    def add_hashtags(self, caption: str, hashtags: list) -> str:
        """Add hashtags to caption."""
        if hashtags:
            hashtag_str = " " + " ".join(hashtags)
            return caption + hashtag_str
        return caption

    def validate_video(self, video_path: str) -> tuple[bool, str]:
        """Legacy validate video method - redirects to advanced validation."""
        is_valid, message, metadata = self.validate_video_advanced(video_path)
        return is_valid, message

    def get_posting_stats(self) -> Dict[str, int]:
        """Get enhanced posting statistics including rate limiting info."""
        stats = {
            'total': len(self.queue.queue),
            'pending': 0,
            'posted': 0,
            'failed': 0,
            'skipped': 0,
            'rate_limited': 0,
            'session_expired': 0,
            'challenge_required': 0,
            'posts_today': len(self.posting_history),
            'remaining_posts_today': 0
        }

        # Count queue status
        for item in self.queue.queue:
            status = item.get('status', 'pending')
            if status in stats:
                stats[status] += 1

        # Calculate remaining posts
        can_post, remaining = self.check_rate_limit()
        stats['remaining_posts_today'] = remaining

        return stats

    def diagnose_instagram_connection(self) -> Dict[str, Any]:
        """Enhanced Instagram connection and API diagnostics."""
        diagnostics = {
            'login_status': False,
            'session_valid': False,
            'api_accessible': False,
            'rate_limit_status': 'Unknown',
            'posting_stats': {},
            'recommendations': []
        }

        # Get posting statistics
        diagnostics['posting_stats'] = self.get_posting_stats()

        # Check rate limiting
        can_post, remaining = self.check_rate_limit()
        if can_post:
            diagnostics['rate_limit_status'] = f"Good ({remaining} posts remaining)"
        else:
            diagnostics['rate_limit_status'] = "Rate limited (0 posts remaining)"
            diagnostics['recommendations'].append("Rate limit reached - wait for 24-hour window to reset")

        try:
            # Check if client is initialized
            if not hasattr(self.client, 'user_id') or not self.client.user_id:
                diagnostics['recommendations'].append("Not logged in to Instagram")
                return diagnostics

            # Try multiple verification methods
            verification_methods = [
                ('Timeline Feed', lambda: self.client.get_timeline_feed()),
                ('User Info', lambda: self.client.user_info(self.client.user_id)),
                ('Account Info', lambda: self.client.account_info())
            ]

            for method_name, method_func in verification_methods:
                try:
                    result = method_func()
                    if result:
                        diagnostics['login_status'] = True
                        diagnostics['session_valid'] = True
                        diagnostics['api_accessible'] = True
                        logger.info(f"Instagram API verified via {method_name}")
                        break
                except Exception as e:
                    error_str = str(e).lower()
                    logger.warning(f"{method_name} verification failed: {e}")

                    if "login" in error_str or "session" in error_str:
                        diagnostics['recommendations'].append("Session expired - please login again")
                    elif "data" in error_str:
                        diagnostics['recommendations'].append("Instagram API format changed - try updating instagrapi")
                    elif "challenge" in error_str:
                        diagnostics['recommendations'].append("Instagram challenge required - resolve manually")
                    elif "rate" in error_str:
                        diagnostics['recommendations'].append("Instagram rate limit hit - wait before trying again")

        except Exception as e:
            diagnostics['recommendations'].append(f"Client initialization error: {str(e)}")

        # Provide additional recommendations
        if not diagnostics['api_accessible']:
            diagnostics['recommendations'].extend([
                "Try clearing session.json and logging in again",
                "Update instagrapi: pip install --upgrade instagrapi",
                "Check if account is Business or Creator type",
                "Verify Instagram account is not restricted"
            ])

        return diagnostics

    def clear_session_cache(self) -> bool:
        """Clear session cache to force fresh login."""
        try:
            if hasattr(self.client, '_request'):
                self.client._request = None
            if hasattr(self.client, '_session'):
                self.client._session = None
            logger.info("Session cache cleared")
            return True
        except Exception as e:
            logger.error(f"Failed to clear session cache: {e}")
            return False

    def test_video_upload_prerequisites(self, video_path: str) -> Dict[str, Any]:
        """Enhanced test of video upload prerequisites."""
        results = {
            'file_exists': False,
            'file_valid': False,
            'rate_limit_ok': False,
            'login_status': False,
            'can_upload': False,
            'metadata': {},
            'issues': []
        }

        # Check file existence
        if not Path(video_path).exists():
            results['issues'].append("Video file does not exist")
            return results

        results['file_exists'] = True

        # Enhanced video validation
        is_valid, validation_msg, metadata = self.validate_video_advanced(video_path)
        results['file_valid'] = is_valid
        results['metadata'] = metadata
        if not is_valid:
            results['issues'].append(f"Video validation failed: {validation_msg}")

        # Check rate limiting
        can_post, remaining = self.check_rate_limit()
        results['rate_limit_ok'] = can_post
        if not can_post:
            results['issues'].append("Rate limit exceeded - 0 posts remaining today")

        # Check login status
        try:
            if hasattr(self.client, 'user_id') and self.client.user_id:
                user = self.client.user_info(self.client.user_id)
                results['login_status'] = True
            else:
                results['issues'].append("Not logged in to Instagram")
        except Exception as e:
            results['issues'].append(f"Login check failed: {str(e)}")

        # Determine if upload is possible
        results['can_upload'] = (
            results['file_exists'] and 
            results['file_valid'] and 
            results['rate_limit_ok'] and 
            results['login_status']
        )

        return results

    def get_rate_limit_info(self) -> Dict[str, Any]:
        """Get detailed rate limiting information."""
        can_post, remaining = self.check_rate_limit()
        
        info = {
            'can_post': can_post,
            'remaining_posts': remaining,
            'max_posts_per_day': self.max_posts_per_day,
            'posts_today': len(self.posting_history),
            'history_count': len(self.posting_history)
        }
        
        if self.posting_history:
            latest_post = max(self.posting_history, key=lambda x: x['timestamp'])
            info['last_post_time'] = latest_post['timestamp']
        else:
            info['last_post_time'] = None
            
        return info
