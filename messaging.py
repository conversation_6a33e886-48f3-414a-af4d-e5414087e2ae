"""
Instagram Direct Messaging and Comments Module
Handles account discovery, direct messaging, and comment functionality
with proper rate limiting and error handling.
"""
import logging
import time
import threading
import queue
import json
import hashlib
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import random
import concurrent.futures

from instagrapi import Client
from instagrapi.exceptions import (
    <PERSON><PERSON>Required, ClientError, TwoFactorRequired, BadPassword,
    ChallengeRequired, DirectError, DirectThreadNotFound,
    DirectMessageNotFound, RateLimitError
)

import requests
import urllib.parse

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Types of messages that can be sent."""
    DIRECT_MESSAGE = "direct_message"
    COMMENT = "comment"


class ScheduleType(Enum):
    """Types of scheduling options."""
    NOW = "now"
    SCHEDULED = "scheduled"
    RECURRING = "recurring"


@dataclass
class DiscoveredUser:
    """Represents a discovered Instagram user."""
    user_id: str
    username: str
    fullname: str
    follower_count: Optional[int] = None
    following_count: Optional[int] = None
    is_verified: bool = False
    is_private: bool = False
    source: str = ""  # hashtag, username_search, etc.


@dataclass
class MessageTask:
    """Represents a message sending task."""
    task_id: str
    user_id: str
    username: str
    message: str
    message_type: MessageType
    schedule_type: ScheduleType
    scheduled_time: Optional[datetime] = None
    recurring_config: Optional[Dict[str, Any]] = None
    status: str = "pending"  # pending, sending, sent, failed
    error_message: Optional[str] = None
    created_at: datetime = None
    completed_at: Optional[datetime] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class RateLimiter:
    """Handles Instagram API rate limiting with optimized performance for bulk operations."""

    def __init__(self, requests_per_hour: int = 500):  # Increased for bulk operations
        self.requests_per_hour = requests_per_hour
        self.requests_per_minute = max(5, requests_per_hour // 60)  # Increased per-minute limit
        self.hourly_requests = []
        self.minutely_requests = []
        self.lock = threading.Lock()
        self.last_request_time = 0
        self.min_delay = 0.05  # Reduced minimum delay for bulk operations (50ms)
        
        # Bulk operation optimizations
        self.bulk_mode = False
        self.bulk_batch_delay = 2.0  # Delay between batches in bulk mode

    def can_make_request(self) -> bool:
        """Check if we can make a request without exceeding rate limits."""
        with self.lock:
            now = datetime.now()

            # Clean old requests
            cutoff_hour = now - timedelta(hours=1)
            cutoff_minute = now - timedelta(minutes=1)

            self.hourly_requests = [t for t in self.hourly_requests if t > cutoff_hour]
            self.minutely_requests = [t for t in self.minutely_requests if t > cutoff_minute]

            # Check limits
            if len(self.hourly_requests) >= self.requests_per_hour:
                return False
            if len(self.minutely_requests) >= self.requests_per_minute:
                return False

            return True

    def record_request(self):
        """Record a request being made."""
        with self.lock:
            now = datetime.now()
            self.hourly_requests.append(now)
            self.minutely_requests.append(now)
            self.last_request_time = time.time()  # Update last request timestamp

    def enable_bulk_mode(self):
        """Enable bulk mode for optimized batch processing."""
        self.bulk_mode = True
        self.min_delay = 0.02  # Further reduced delay for bulk mode (20ms)
        logger.info("Rate limiter: Bulk mode enabled")
    
    def disable_bulk_mode(self):
        """Disable bulk mode and return to normal rate limiting."""
        self.bulk_mode = False
        self.min_delay = 0.1  # Return to normal delay
        logger.info("Rate limiter: Bulk mode disabled")

    def get_wait_time(self) -> float:
        """Get seconds to wait before next request with optimized delays."""
        with self.lock:
            current_time = time.time()
            
            # Check minimum delay since last request
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.min_delay:
                return self.min_delay - time_since_last
            
            if not self.can_make_request():
                now = datetime.now()
                if self.hourly_requests:
                    oldest_hour = min(self.hourly_requests)
                    wait_hour = (oldest_hour + timedelta(hours=1) - now).total_seconds()
                else:
                    wait_hour = 0

                if self.minutely_requests:
                    oldest_minute = min(self.minutely_requests)
                    wait_minute = (oldest_minute + timedelta(minutes=1) - now).total_seconds()
                else:
                    wait_minute = 0

                # In bulk mode, reduce wait times
                if self.bulk_mode:
                    return max(wait_hour, wait_minute, 0.5)  # Shorter wait in bulk mode
                else:
                    return max(wait_hour, wait_minute, 1.0)
            return 0
    
    def get_bulk_batch_delay(self) -> float:
        """Get optimal delay between batches in bulk mode."""
        return self.bulk_batch_delay if self.bulk_mode else 5.0


class InstagramBusinessAPI:
    """Handles Instagram Graph API and Messenger API for business accounts with better rate limits."""
    
    def __init__(self, access_token: str = None, business_account_id: str = None):
        self.access_token = access_token
        self.business_account_id = business_account_id
        self.graph_api_base = "https://graph.facebook.com/v18.0"
        self.messenger_api_base = "https://graph.facebook.com/v18.0"
        self.rate_limit_per_hour = 4800  # Graph API allows 4800 requests per hour
        
    def set_credentials(self, access_token: str, business_account_id: str):
        """Set Instagram Business API credentials."""
        self.access_token = access_token
        self.business_account_id = business_account_id
        logger.info("Instagram Business API credentials configured")
    
    def is_configured(self) -> bool:
        """Check if Business API is properly configured."""
        return bool(self.access_token and self.business_account_id)
    
    def get_user_info_batch(self, usernames: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get user information for multiple users using Instagram Graph API."""
        if not self.is_configured():
            logger.warning("Instagram Business API not configured")
            return {}
            
        results = {}
        
        # Process in batches of 50 (Graph API limit)
        batch_size = 50
        for i in range(0, len(usernames), batch_size):
            batch = usernames[i:i + batch_size]
            batch_results = self._get_user_info_batch_request(batch)
            results.update(batch_results)
            
            # Rate limiting
            if i + batch_size < len(usernames):
                time.sleep(1)  # Delay between batches
        
        return results
    
    def _get_user_info_batch_request(self, usernames: List[str]) -> Dict[str, Dict[str, Any]]:
        """Make a batch request to get user information."""
        try:
            # Construct batch request
            batch_requests = []
            for idx, username in enumerate(usernames):
                batch_requests.append({
                    "method": "GET",
                    "relative_url": f"ig_user?fields=id,username,account_type,media_count,followers_count&username={username}"
                })
            
            url = f"{self.graph_api_base}/"
            payload = {
                "access_token": self.access_token,
                "batch": json.dumps(batch_requests)
            }
            
            response = requests.post(url, data=payload)
            response.raise_for_status()
            
            results = {}
            batch_responses = response.json()
            
            for idx, batch_response in enumerate(batch_responses):
                username = usernames[idx] if idx < len(usernames) else f"unknown_{idx}"
                
                if batch_response.get("code") == 200:
                    user_data = json.loads(batch_response["body"])
                    results[username] = {
                        "user_id": user_data.get("id"),
                        "username": user_data.get("username"),
                        "account_type": user_data.get("account_type"),
                        "followers_count": user_data.get("followers_count"),
                        "media_count": user_data.get("media_count")
                    }
                else:
                    logger.debug(f"Failed to get info for {username}: {batch_response}")
                    results[username] = None
            
            return results
            
        except requests.RequestException as e:
            logger.error(f"Graph API batch request failed: {e}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error in batch user info request: {e}")
            return {}
    
    def send_message_via_messenger_api(self, recipient_id: str, message: str) -> bool:
        """Send message using Instagram Messenger API (for business accounts)."""
        if not self.is_configured():
            logger.warning("Instagram Business API not configured")
            return False
            
        try:
            url = f"{self.messenger_api_base}/{self.business_account_id}/messages"
            
            payload = {
                "recipient": {"id": recipient_id},
                "message": {"text": message},
                "access_token": self.access_token
            }
            
            response = requests.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            if result.get("message_id"):
                logger.info(f"✅ Sent message via Messenger API to {recipient_id}")
                return True
            else:
                logger.error(f"❌ No message_id in Messenger API response: {result}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"Messenger API request failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in Messenger API: {e}")
            return False
    
    def bulk_send_messages_via_api(self, recipient_message_pairs: List[Tuple[str, str]], progress_callback=None) -> Dict[str, Any]:
        """Send bulk messages using Instagram Messenger API with optimal rate limiting."""
        if not self.is_configured():
            return {"error": "Instagram Business API not configured"}
        
        logger.info(f"Starting bulk messaging via Instagram API to {len(recipient_message_pairs)} recipients")
        
        if progress_callback:
            progress_callback(f"Starting bulk messaging via Instagram API...")
        
        sent_count = 0
        failed_count = 0
        
        # Process messages with optimal rate limiting (4800/hour = 1.33/second)
        for idx, (recipient_id, message) in enumerate(recipient_message_pairs):
            if self.send_message_via_messenger_api(recipient_id, message):
                sent_count += 1
            else:
                failed_count += 1
            
            # Progress update
            if progress_callback and (idx + 1) % 10 == 0:
                progress_callback(f"Processed {idx + 1}/{len(recipient_message_pairs)} messages...")
            
            # Rate limiting: 1.3 requests per second max
            if idx < len(recipient_message_pairs) - 1:
                time.sleep(0.8)  # Slightly under the limit for safety
        
        results = {
            "api_method": "Instagram Messenger API",
            "total_messages": len(recipient_message_pairs),
            "sent_count": sent_count,
            "failed_count": failed_count,
            "success_rate": (sent_count / len(recipient_message_pairs) * 100) if recipient_message_pairs else 0
        }
        
        logger.info(f"Bulk messaging via API complete: {sent_count} sent, {failed_count} failed")
        
        if progress_callback:
            progress_callback(f"Complete: {sent_count} sent, {failed_count} failed via Instagram API")
        
        return results
    
    def get_api_rate_limit_status(self) -> Dict[str, Any]:
        """Get current API rate limit status."""
        if not self.is_configured():
            return {"error": "API not configured"}
        
        try:
            url = f"{self.graph_api_base}/{self.business_account_id}"
            params = {
                "fields": "rate_limit_info",
                "access_token": self.access_token
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            rate_info = data.get("rate_limit_info", {})
            
            return {
                "hourly_limit": self.rate_limit_per_hour,
                "current_usage": rate_info.get("call_count", 0),
                "percentage_used": (rate_info.get("call_count", 0) / self.rate_limit_per_hour * 100),
                "time_until_reset": rate_info.get("time_until_reset", 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to get API rate limit status: {e}")
            return {"error": str(e)}


class UserCacheManager:
    """Manages caching of resolved user IDs to improve bulk messaging efficiency."""
    
    def __init__(self, account_id: str = "default"):
        self.account_id = account_id
        self.cache_file = Path(f"accounts/{account_id}/user_cache.json")
        self.cache_file.parent.mkdir(parents=True, exist_ok=True)
        self.cache = {}
        self.cache_expiry_hours = 24  # Cache expires after 24 hours
        self._load_cache()
    
    def _load_cache(self):
        """Load user ID cache from file."""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Clean expired entries
                    now = datetime.now()
                    self.cache = {
                        username: entry for username, entry in data.items()
                        if datetime.fromisoformat(entry['cached_at']) + timedelta(hours=self.cache_expiry_hours) > now
                    }
                    logger.info(f"Loaded {len(self.cache)} cached user IDs")
        except Exception as e:
            logger.warning(f"Could not load user cache: {e}")
            self.cache = {}
    
    def _save_cache(self):
        """Save user ID cache to file."""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save user cache: {e}")
    
    def get_user_id(self, username: str) -> Optional[str]:
        """Get cached user ID for username."""
        username_lower = username.lower()
        if username_lower in self.cache:
            entry = self.cache[username_lower]
            cached_time = datetime.fromisoformat(entry['cached_at'])
            if datetime.now() - cached_time < timedelta(hours=self.cache_expiry_hours):
                return entry['user_id']
            else:
                # Remove expired entry
                del self.cache[username_lower]
        return None
    
    def cache_user_id(self, username: str, user_id: str):
        """Cache a resolved user ID."""
        username_lower = username.lower()
        self.cache[username_lower] = {
            'user_id': user_id,
            'username': username,
            'cached_at': datetime.now().isoformat()
        }
        # Save cache periodically
        if len(self.cache) % 10 == 0:
            self._save_cache()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            'total_cached': len(self.cache),
            'cache_file': str(self.cache_file),
            'expiry_hours': self.cache_expiry_hours
        }


class InstagramMessaging:
    """Handles Instagram messaging operations with proper rate limiting and error handling."""

    def __init__(self, client: Client, account_id: str = "default"):
        self.client = client
        self.account_id = account_id
        self.rate_limiter = RateLimiter()
        self.message_queue = queue.Queue()
        self.task_history = []
        self.is_running = False
        self.worker_thread = None
        
        # Initialize user cache manager
        self.user_cache = UserCacheManager(account_id)
        
        # Initialize Instagram Business API
        self.business_api = InstagramBusinessAPI()
        
        # Batch processing settings
        self.batch_size = 20  # Process usernames in batches
        self.max_workers = 3  # Concurrent workers for bulk operations
        
        # API preference settings
        self.prefer_business_api = True  # Prefer official API when available

        # Load existing task history
        self._load_task_history()
    
    def configure_business_api(self, access_token: str, business_account_id: str) -> bool:
        """Configure Instagram Business API for enhanced messaging capabilities."""
        try:
            self.business_api.set_credentials(access_token, business_account_id)
            
            # Test the API configuration
            status = self.business_api.get_api_rate_limit_status()
            if "error" not in status:
                logger.info("✅ Instagram Business API configured successfully")
                return True
            else:
                logger.error(f"❌ Instagram Business API configuration failed: {status['error']}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to configure Instagram Business API: {e}")
            return False
    
    def get_messaging_capabilities(self) -> Dict[str, Any]:
        """Get current messaging capabilities and API status."""
        capabilities = {
            "instagrapi_available": bool(self.client),
            "business_api_available": self.business_api.is_configured(),
            "preferred_method": "business_api" if self.prefer_business_api and self.business_api.is_configured() else "instagrapi",
            "rate_limits": {
                "instagrapi": {
                    "hourly_limit": self.rate_limiter.requests_per_hour,
                    "current_usage": len(self.rate_limiter.hourly_requests)
                }
            }
        }
        
        if self.business_api.is_configured():
            api_status = self.business_api.get_api_rate_limit_status()
            capabilities["rate_limits"]["business_api"] = api_status
        
        return capabilities

    def _load_task_history(self):
        """Load message task history from file."""
        try:
            import json
            from pathlib import Path

            history_file = Path(f"accounts/{self.account_id}/message_history.json")
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # TODO: Load and reconstruct MessageTask objects
        except Exception as e:
            logger.warning(f"Could not load message history: {e}")

    def _save_task_history(self):
        """Save message task history to file."""
        try:
            import json
            from pathlib import Path

            history_file = Path(f"accounts/{self.account_id}/message_history.json")
            history_file.parent.mkdir(parents=True, exist_ok=True)

            # Convert tasks to serializable format
            tasks_data = []
            for task in self.task_history[-1000:]:  # Keep last 1000 tasks
                tasks_data.append({
                    'task_id': task.task_id,
                    'user_id': task.user_id,
                    'username': task.username,
                    'message': task.message,
                    'message_type': task.message_type.value,
                    'schedule_type': task.schedule_type.value,
                    'scheduled_time': task.scheduled_time.isoformat() if task.scheduled_time else None,
                    'recurring_config': task.recurring_config,
                    'status': task.status,
                    'error_message': task.error_message,
                    'created_at': task.created_at.isoformat(),
                    'completed_at': task.completed_at.isoformat() if task.completed_at else None
                })

            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Could not save message history: {e}")

    def discover_users_by_hashtag(self, hashtag: str, amount: int = 50) -> List[DiscoveredUser]:
        """Discover users who have posted with a specific hashtag."""
        if not hashtag.startswith('#'):
            hashtag = f"#{hashtag}"

        hashtag_name = hashtag[1:]  # Remove # for API
        users = []

        try:
            # Wait for rate limit
            wait_time = self.rate_limiter.get_wait_time()
            if wait_time > 0:
                logger.info(f"Rate limited, waiting {wait_time:.1f} seconds")
                time.sleep(wait_time)

            # Get recent media for hashtag
            medias = self.client.hashtag_medias_recent(hashtag_name, amount=min(amount * 2, 100))

            self.rate_limiter.record_request()

            # Extract unique users from media
            seen_users = set()

            for media in medias:
                user_id = str(media.user.pk)
                if user_id not in seen_users and len(users) < amount:
                    user_info = self.client.user_info(user_id)
                    self.rate_limiter.record_request()

                    user = DiscoveredUser(
                        user_id=user_id,
                        username=user_info.username,
                        fullname=user_info.full_name,
                        follower_count=getattr(user_info, 'follower_count', None),
                        following_count=getattr(user_info, 'following_count', None),
                        is_verified=user_info.is_verified,
                        is_private=user_info.is_private,
                        source=f"hashtag:{hashtag}"
                    )
                    users.append(user)
                    seen_users.add(user_id)

                    # Add small delay between user info requests
                    time.sleep(random.uniform(1, 3))

        except Exception as e:
            logger.error(f"Error discovering users by hashtag {hashtag}: {e}")

        return users

    def discover_users_by_username_search(self, query: str, amount: int = 20) -> List[DiscoveredUser]:
        """Search for users by username or name."""
        users = []

        try:
            # Wait for rate limit
            wait_time = self.rate_limiter.get_wait_time()
            if wait_time > 0:
                time.sleep(wait_time)

            # Search users
            search_results = self.client.search_users(query, amount=amount)
            self.rate_limiter.record_request()

            for result in search_results:
                user = DiscoveredUser(
                    user_id=str(result.pk),
                    username=result.username,
                    fullname=result.full_name,
                    follower_count=getattr(result, 'follower_count', None),
                    following_count=getattr(result, 'following_count', None),
                    is_verified=getattr(result, 'is_verified', False),
                    is_private=getattr(result, 'is_private', False),
                    source="username_search"
                )
                users.append(user)

        except Exception as e:
            logger.error(f"Error searching users with query '{query}': {e}")

        return users

    def send_direct_message(self, user_id: str, message: str) -> bool:
        """Send a direct message to a user with robust error handling."""
        try:
            # Validate inputs
            if not user_id:
                logger.error("Cannot send DM: user_id is None or empty")
                return False
            
            if not message or not message.strip():
                logger.error("Cannot send DM: message is empty")
                return False
            
            # Convert user_id to string if it's not already
            user_id = str(user_id)
            
            # Validate user_id format (should be numeric)
            if not user_id.isdigit():
                logger.error(f"Invalid user_id format: {user_id}")
                return False

            # Wait for rate limit with timeout
            wait_time = self.rate_limiter.get_wait_time()
            if wait_time > 0:
                if wait_time > 10:  # Cap maximum wait time to prevent hanging
                    logger.warning(f"Long wait time ({wait_time:.1f}s) capped to 10s")
                    wait_time = 10
                logger.info(f"Rate limited, waiting {wait_time:.1f} seconds")
                time.sleep(wait_time)

            # Send message using the latest instagrapi method
            logger.debug(f"Attempting to send DM to user {user_id}")
            result = self.client.direct_send(message, user_ids=[int(user_id)])
            self.rate_limiter.record_request()

            if result:
                logger.info(f"✅ Sent DM to user {user_id}: {message[:50]}...")
                return True
            else:
                logger.error(f"❌ Failed to send DM to user {user_id}: No result returned")
                return False

        except RateLimitError as e:
            logger.warning(f"⏰ Rate limit exceeded for DM sending: {e}")
            return False
        except DirectError as e:
            logger.error(f"📩 Direct messaging error for user {user_id}: {e}")
            return False
        except ValueError as e:
            logger.error(f"🔢 Invalid user_id format for user {user_id}: {e}")
            return False
        except LoginRequired as e:
            logger.error(f"🔐 Login required for sending DM: {e}")
            return False
        except ClientError as e:
            error_msg = str(e).lower()
            if "rate limit" in error_msg:
                logger.warning(f"⏰ Rate limit exceeded (ClientError): {e}")
            elif "user not found" in error_msg:
                logger.error(f"👤 User not found for ID {user_id}: {e}")
            elif "blocked" in error_msg:
                logger.error(f"🚫 User {user_id} has blocked this account: {e}")
            elif "challenge" in error_msg:
                logger.error(f"🔒 Challenge required - account may be flagged: {e}")
            else:
                logger.error(f"⚠️ Client error sending DM to user {user_id}: {e}")
            return False
        except TimeoutError as e:
            logger.error(f"⏱️ Timeout sending DM to user {user_id}: {e}")
            return False
        except ConnectionError as e:
            logger.error(f"🌐 Connection error sending DM to user {user_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"💥 Unexpected error sending DM to user {user_id}: {e}")
            return False

    def resolve_user_id(self, username: str) -> Optional[str]:
        """Resolve username to user ID with caching and robust error handling."""
        try:
            # Validate input
            if not username or not username.strip():
                logger.debug("Cannot resolve user ID: username is empty")
                return None
            
            # Clean username
            username = username.strip()
            if username.startswith('@'):
                username = username[1:]
            
            if not username:
                logger.debug("Cannot resolve user ID: username is empty after cleaning")
                return None

            # Check cache first
            cached_user_id = self.user_cache.get_user_id(username)
            if cached_user_id:
                logger.debug(f"Cache hit: @{username} → ID: {cached_user_id}")
                return cached_user_id

            # If not in cache, resolve it
            user_id = self._resolve_single_username(username)
            
            # Cache the result if successful
            if user_id:
                self.user_cache.cache_user_id(username, user_id)
                logger.debug(f"Resolved and cached: @{username} → ID: {user_id}")
            
            return user_id

        except Exception as e:
            logger.debug(f"Unexpected error resolving user ID for @{username}: {e}")
            return None

    def _resolve_single_username(self, username: str) -> Optional[str]:
        """Resolve a single username to user ID using various API methods."""
        try:
            # Optimized rate limiting with shorter waits
            wait_time = self.rate_limiter.get_wait_time()
            if wait_time > 0:
                if wait_time > 0.5:  # Only log significant waits
                    logger.debug(f"Rate limited, waiting {wait_time:.1f}s for @{username}")
                time.sleep(wait_time)

            # Method 1: Try the fastest direct method first
            try:
                user_id = self.client.user_id_from_username(username)
                self.rate_limiter.record_request()
                
                if user_id:
                    user_id_str = str(user_id)
                    return user_id_str
                    
            except Exception as fast_e:
                logger.debug(f"Fast method failed for @{username}: {fast_e}")

            # Method 2: Try using private API method (more reliable)
            try:
                user_info = self.client.user_info_by_username_v1(username)
                self.rate_limiter.record_request()

                if user_info and hasattr(user_info, 'pk'):
                    user_id = str(user_info.pk)
                    return user_id
                    
            except Exception as v1_e:
                logger.debug(f"Private API method failed for @{username}: {v1_e}")

            # Method 3: Fallback to standard method (may have compatibility issues)
            try:
                user_info = self.client.user_info_by_username(username)
                self.rate_limiter.record_request()

                if user_info and hasattr(user_info, 'pk'):
                    user_id = str(user_info.pk)
                    return user_id
                else:
                    logger.debug(f"Standard method returned info but no pk for @{username}")
                    return None
                    
            except TypeError as type_e:
                if "unexpected keyword argument" in str(type_e):
                    logger.warning(f"API compatibility issue for @{username} - trying alternative method")
                    return self._alternative_user_resolution(username)
                else:
                    logger.debug(f"Type error in standard method for @{username}: {type_e}")
                    return None
            except Exception as std_e:
                logger.debug(f"Standard method failed for @{username}: {std_e}")
                return None

        except ClientError as e:
            error_msg = str(e).lower()
            if "user not found" in error_msg:
                logger.debug(f"User @{username} not found")
            elif "rate limit" in error_msg:
                logger.warning(f"Rate limit exceeded while resolving @{username}")
            else:
                logger.debug(f"Client error resolving @{username}: {e}")
            return None
        except LoginRequired as e:
            logger.error(f"Login required to resolve @{username}: {e}")
            return None
        except Exception as e:
            logger.debug(f"Unexpected error resolving user ID for @{username}: {e}")
            return None

    def resolve_usernames_batch(self, usernames: List[str], progress_callback=None) -> Dict[str, Optional[str]]:
        """Efficiently resolve multiple usernames to user IDs using batch processing and caching."""
        logger.info(f"Starting batch resolution for {len(usernames)} usernames")
        
        # Clean and validate usernames
        cleaned_usernames = []
        for username in usernames:
            if username and username.strip():
                cleaned = username.strip()
                if cleaned.startswith('@'):
                    cleaned = cleaned[1:]
                if cleaned:
                    cleaned_usernames.append(cleaned)
        
        results = {}
        cache_hits = 0
        api_calls_needed = []
        
        # Step 1: Check cache for all usernames
        for username in cleaned_usernames:
            cached_user_id = self.user_cache.get_user_id(username)
            if cached_user_id:
                results[username] = cached_user_id
                cache_hits += 1
            else:
                api_calls_needed.append(username)
        
        logger.info(f"Cache hits: {cache_hits}/{len(cleaned_usernames)}, API calls needed: {len(api_calls_needed)}")
        
        if progress_callback:
            progress_callback(f"Cache hits: {cache_hits}, resolving {len(api_calls_needed)} usernames...")
        
        # Step 2: Batch process usernames that need API calls
        if api_calls_needed:
            # Split into smaller batches to respect rate limits
            batches = [api_calls_needed[i:i + self.batch_size] for i in range(0, len(api_calls_needed), self.batch_size)]
            
            for batch_idx, batch in enumerate(batches):
                logger.info(f"Processing batch {batch_idx + 1}/{len(batches)} ({len(batch)} usernames)")
                
                if progress_callback:
                    progress_callback(f"Processing batch {batch_idx + 1}/{len(batches)}...")
                
                # Process batch with concurrent workers (limited to respect rate limits)
                batch_results = self._process_username_batch(batch)
                results.update(batch_results)
                
                # Add delay between batches to respect rate limits
                if batch_idx < len(batches) - 1:
                    delay = random.uniform(2, 5)  # Random delay between batches
                    logger.debug(f"Waiting {delay:.1f}s before next batch")
                    time.sleep(delay)
        
        # Step 3: Save cache
        self.user_cache._save_cache()
        
        successful_resolutions = len([r for r in results.values() if r is not None])
        logger.info(f"Batch resolution complete: {successful_resolutions}/{len(cleaned_usernames)} successful")
        
        if progress_callback:
            progress_callback(f"Complete: {successful_resolutions}/{len(cleaned_usernames)} usernames resolved")
        
        return results

    def _process_username_batch(self, usernames: List[str]) -> Dict[str, Optional[str]]:
        """Process a batch of usernames with controlled concurrency."""
        results = {}
        
        # Use ThreadPoolExecutor for controlled concurrency
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all username resolution tasks
            future_to_username = {
                executor.submit(self._resolve_single_username, username): username 
                for username in usernames
            }
            
            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_username):
                username = future_to_username[future]
                try:
                    user_id = future.result()
                    results[username] = user_id
                    
                    # Cache successful resolution
                    if user_id:
                        self.user_cache.cache_user_id(username, user_id)
                        logger.debug(f"Batch resolved: @{username} → ID: {user_id}")
                    else:
                        logger.debug(f"Batch failed to resolve: @{username}")
                        
                except Exception as e:
                    logger.error(f"Error resolving @{username} in batch: {e}")
                    results[username] = None
        
        return results

    def send_bulk_messages_optimized(self, username_message_pairs: List[Tuple[str, str]], progress_callback=None) -> Dict[str, Any]:
        """Send messages to multiple users with the most efficient method available."""
        logger.info(f"Starting optimized bulk messaging to {len(username_message_pairs)} users")
        
        # Choose the best available method
        if self.prefer_business_api and self.business_api.is_configured():
            return self._send_bulk_via_business_api(username_message_pairs, progress_callback)
        else:
            return self._send_bulk_via_instagrapi(username_message_pairs, progress_callback)
    
    def _send_bulk_via_business_api(self, username_message_pairs: List[Tuple[str, str]], progress_callback=None) -> Dict[str, Any]:
        """Send bulk messages using Instagram Business API (fastest and most reliable)."""
        logger.info(f"Using Instagram Business API for bulk messaging to {len(username_message_pairs)} users")
        
        if progress_callback:
            progress_callback("Using Instagram Business API for optimal performance...")
        
        # Step 1: Extract unique usernames for batch resolution
        unique_usernames = list(set([pair[0] for pair in username_message_pairs]))
        
        if progress_callback:
            progress_callback(f"Batch resolving {len(unique_usernames)} usernames via Graph API...")
        
        # Step 2: Use Graph API for batch username resolution (much faster)
        user_info_batch = self.business_api.get_user_info_batch(unique_usernames)
        
        # Step 3: Prepare user_id to message pairs
        user_id_message_pairs = []
        resolution_stats = {"successful": 0, "failed": 0}
        
        for username, message in username_message_pairs:
            user_info = user_info_batch.get(username)
            if user_info and user_info.get("user_id"):
                user_id_message_pairs.append((user_info["user_id"], message))
                resolution_stats["successful"] += 1
                
                # Cache the resolved user
                self.user_cache.cache_user_id(username, user_info["user_id"])
            else:
                resolution_stats["failed"] += 1
                logger.debug(f"Failed to resolve {username} via Business API")
        
        # Step 4: Send messages via Messenger API
        if progress_callback:
            progress_callback(f"Sending {len(user_id_message_pairs)} messages via Messenger API...")
        
        messaging_results = self.business_api.bulk_send_messages_via_api(user_id_message_pairs, progress_callback)
        
        # Step 5: Compile comprehensive results
        results = {
            "method": "Instagram Business API",
            "total_users": len(username_message_pairs),
            "unique_usernames": len(unique_usernames),
            "resolution_successful": resolution_stats["successful"],
            "resolution_failed": resolution_stats["failed"],
            "messages_sent": messaging_results.get("sent_count", 0),
            "messages_failed": messaging_results.get("failed_count", 0),
            "success_rate": messaging_results.get("success_rate", 0),
            "api_rate_limit_status": self.business_api.get_api_rate_limit_status(),
            "cache_stats": self.user_cache.get_cache_stats()
        }
        
        logger.info(f"Business API bulk messaging complete: {results['messages_sent']} sent, {results['messages_failed']} failed")
        return results
    
    def _send_bulk_via_instagrapi(self, username_message_pairs: List[Tuple[str, str]], progress_callback=None) -> Dict[str, Any]:
        """Send bulk messages using instagrapi (fallback method)."""
        logger.info(f"Using instagrapi for bulk messaging to {len(username_message_pairs)} users")
        
        if progress_callback:
            progress_callback("Using instagrapi method...")
        
        # Enable bulk mode for optimized rate limiting
        self.rate_limiter.enable_bulk_mode()
        
        try:
            # Step 1: Extract unique usernames for batch resolution
            unique_usernames = list(set([pair[0] for pair in username_message_pairs]))
            
            if progress_callback:
                progress_callback(f"Resolving {len(unique_usernames)} unique usernames...")
            
            # Step 2: Batch resolve all usernames
            resolved_users = self.resolve_usernames_batch(unique_usernames, progress_callback)
            
            # Step 3: Prepare messaging tasks
            successful_resolutions = 0
            failed_resolutions = 0
            messages_sent = 0
            messages_failed = 0
            
            for username, message in username_message_pairs:
                user_id = resolved_users.get(username)
                
                if user_id:
                    successful_resolutions += 1
                    # Send message
                    if self.send_direct_message(user_id, message):
                        messages_sent += 1
                        logger.info(f"✅ Sent message to @{username}")
                    else:
                        messages_failed += 1
                        logger.error(f"❌ Failed to send message to @{username}")
                    
                    # Optimized delay between messages in bulk mode
                    time.sleep(random.uniform(0.5, 1.5))
                else:
                    failed_resolutions += 1
                    logger.error(f"❌ Could not resolve username: @{username}")
            
            # Step 4: Compile results
            results = {
                "method": "instagrapi",
                "total_users": len(username_message_pairs),
                "unique_usernames": len(unique_usernames),
                "resolution_successful": successful_resolutions,
                "resolution_failed": failed_resolutions,
                "messages_sent": messages_sent,
                "messages_failed": messages_failed,
                "success_rate": (messages_sent / len(username_message_pairs) * 100) if username_message_pairs else 0,
                "cache_stats": self.user_cache.get_cache_stats()
            }
            
            logger.info(f"Instagrapi bulk messaging complete: {messages_sent} sent, {messages_failed} failed")
            
            if progress_callback:
                progress_callback(f"Complete: {messages_sent} messages sent, {messages_failed} failed")
            
            return results
            
        finally:
            # Disable bulk mode
            self.rate_limiter.disable_bulk_mode()

    def send_bulk_messages(self, username_message_pairs: List[Tuple[str, str]], progress_callback=None) -> Dict[str, Any]:
        """Legacy method - redirects to optimized bulk messaging."""
        return self.send_bulk_messages_optimized(username_message_pairs, progress_callback)

    def _alternative_user_resolution(self, username: str) -> Optional[str]:
        """Alternative method for resolving user ID when API has compatibility issues."""
        try:
            # Try to search for the user instead
            search_results = self.client.search_users(username, amount=1)
            self.rate_limiter.record_request()
            
            if search_results:
                for user in search_results:
                    if user.username.lower() == username.lower():
                        user_id = str(user.pk)
                        logger.debug(f"Search method resolved @{username} → ID: {user_id}")
                        return user_id
            
            logger.debug(f"Alternative method could not resolve @{username}")
            return None
            
        except Exception as e:
            logger.debug(f"Alternative resolution failed for @{username}: {e}")
            return None

    def add_message_task(self, user: DiscoveredUser, message: str,
                        schedule_type: ScheduleType = ScheduleType.NOW,
                        scheduled_time: Optional[datetime] = None,
                        recurring_config: Optional[Dict[str, Any]] = None) -> str:
        """Add a message task to the queue."""
        import uuid

        task = MessageTask(
            task_id=str(uuid.uuid4()),
            user_id=user.user_id,
            username=user.username,
            message=message,
            message_type=MessageType.DIRECT_MESSAGE,
            schedule_type=schedule_type,
            scheduled_time=scheduled_time,
            recurring_config=recurring_config
        )

        self.message_queue.put(task)
        self.task_history.append(task)

        logger.info(f"Added message task for @{user.username} (ID: {task.task_id})")
        return task.task_id

    def start_message_worker(self):
        """Start the message sending worker thread."""
        if self.is_running:
            return

        self.is_running = True
        self.worker_thread = threading.Thread(target=self._message_worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("Message worker started")

    def stop_message_worker(self):
        """Stop the message sending worker thread."""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        self._save_task_history()
        logger.info("Message worker stopped")

    def _message_worker_loop(self):
        """Main message sending worker loop."""
        while self.is_running:
            try:
                # Get next task
                try:
                    task = self.message_queue.get(timeout=1)
                except queue.Empty:
                    continue

                # Check if task should be executed now
                if not self._should_execute_task(task):
                    # Re-queue for later
                    self.message_queue.put(task)
                    time.sleep(5)  # Wait before checking again
                    continue

                # Execute task
                task.status = "sending"
                success = self._execute_task(task)

                # Update task status
                task.status = "sent" if success else "failed"
                task.completed_at = datetime.now()

                if not success:
                    task.error_message = "Failed to send message"

                # Save history periodically
                if len(self.task_history) % 10 == 0:
                    self._save_task_history()

                self.message_queue.task_done()

            except Exception as e:
                logger.error(f"Error in message worker loop: {e}")
                time.sleep(5)

    def _should_execute_task(self, task: MessageTask) -> bool:
        """Check if a task should be executed now."""
        now = datetime.now()

        if task.schedule_type == ScheduleType.NOW:
            return True
        elif task.schedule_type == ScheduleType.SCHEDULED:
            return task.scheduled_time and now >= task.scheduled_time
        elif task.schedule_type == ScheduleType.RECURRING:
            return self._should_execute_recurring_task(task, now)

        return False

    def _should_execute_recurring_task(self, task: MessageTask, now: datetime) -> bool:
        """Check if a recurring task should be executed now."""
        if not task.recurring_config:
            return False

        config = task.recurring_config
        frequency = config.get("frequency", "daily")
        time_str = config.get("time", "12:00")
        duration_days = config.get("duration_days", 7)

        # Check if we're still within the duration
        if (now - task.created_at).days > duration_days:
            return False  # Recurring period has ended

        try:
            # Parse the target time
            target_hour, target_minute = map(int, time_str.split(":"))

            if frequency == "daily":
                # Check if it's the right time today and we haven't sent today yet
                target_time = now.replace(hour=target_hour, minute=target_minute, second=0, microsecond=0)
                return now >= target_time and not self._task_sent_today(task)

            elif frequency == "weekly":
                # Check if it's the right day and time this week
                if now.weekday() == 0:  # Monday
                    target_time = now.replace(hour=target_hour, minute=target_minute, second=0, microsecond=0)
                    return now >= target_time and not self._task_sent_this_week(task)

            elif frequency == "monthly":
                # Check if it's the right day and time this month
                if now.day == 1:  # First day of month
                    target_time = now.replace(hour=target_hour, minute=target_minute, second=0, microsecond=0)
                    return now >= target_time and not self._task_sent_this_month(task)

        except (ValueError, AttributeError):
            logger.error(f"Invalid recurring config for task {task.task_id}: {config}")

        return False

    def _task_sent_today(self, task: MessageTask) -> bool:
        """Check if this recurring task has been sent today."""
        today = datetime.now().date()
        for history_task in self.task_history:
            if (history_task.username == task.username and
                history_task.message == task.message and
                history_task.status == "sent" and
                history_task.completed_at and
                history_task.completed_at.date() == today):
                return True
        return False

    def _task_sent_this_week(self, task: MessageTask) -> bool:
        """Check if this recurring task has been sent this week."""
        now = datetime.now()
        week_start = now - timedelta(days=now.weekday())
        week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)

        for history_task in self.task_history:
            if (history_task.username == task.username and
                history_task.message == task.message and
                history_task.status == "sent" and
                history_task.completed_at and
                history_task.completed_at >= week_start):
                return True
        return False

    def _task_sent_this_month(self, task: MessageTask) -> bool:
        """Check if this recurring task has been sent this month."""
        now = datetime.now()
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        for history_task in self.task_history:
            if (history_task.username == task.username and
                history_task.message == task.message and
                history_task.status == "sent" and
                history_task.completed_at and
                history_task.completed_at >= month_start):
                return True
        return False

    def _execute_task(self, task: MessageTask) -> bool:
        """Execute a message task."""
        if task.message_type == MessageType.DIRECT_MESSAGE:
            return self.send_direct_message(task.user_id, task.message)
        elif task.message_type == MessageType.COMMENT:
            return self.post_comment(task.user_id, task.message)

        return False

    def post_comment(self, media_id: str, comment: str) -> bool:
        """Post a comment on a media item."""
        try:
            # Wait for rate limit
            wait_time = self.rate_limiter.get_wait_time()
            if wait_time > 0:
                logger.info(f"Rate limited, waiting {wait_time:.1f} seconds")
                time.sleep(wait_time)

            # Post comment
            result = self.client.media_comment(media_id, comment)
            self.rate_limiter.record_request()

            logger.info(f"Posted comment on media {media_id}: {comment[:50]}...")
            return True

        except RateLimitError:
            logger.warning("Rate limit exceeded for commenting")
            return False
        except Exception as e:
            logger.error(f"Unexpected error posting comment on media {media_id}: {e}")
            return False

    def discover_posts_for_commenting(self, hashtag: str, amount: int = 20) -> List[Dict[str, Any]]:
        """Discover recent posts for potential commenting."""
        try:
            # Get recent media for hashtag
            medias = self.client.hashtag_medias_recent(hashtag, amount=min(amount, 50))

            posts = []
            for media in medias:
                posts.append({
                    'media_id': media.id,
                    'user_id': media.user.pk,
                    'username': media.user.username,
                    'caption': getattr(media, 'caption_text', ''),
                    'like_count': getattr(media, 'like_count', 0),
                    'comment_count': getattr(media, 'comment_count', 0),
                    'taken_at': getattr(media, 'taken_at', None)
                })

            return posts

        except Exception as e:
            logger.error(f"Error discovering posts for hashtag {hashtag}: {e}")
            return []

    def get_rate_limit_status(self) -> Dict[str, Any]:
        """Get current rate limiting status."""
        return {
            "hourly_used": len(self.rate_limiter.hourly_requests),
            "hourly_limit": self.rate_limiter.requests_per_hour,
            "minutely_used": len(self.rate_limiter.minutely_requests),
            "minutely_limit": self.rate_limiter.requests_per_minute,
            "can_make_request": self.rate_limiter.can_make_request(),
            "wait_time_seconds": self.rate_limiter.get_wait_time()
        }

    def get_queue_status(self) -> Dict[str, Any]:
        """Get message queue status."""
        pending_tasks = list(self.message_queue.queue) if hasattr(self.message_queue, 'queue') else []

        return {
            "queue_size": self.message_queue.qsize(),
            "pending_tasks": len(pending_tasks),
            "total_history": len(self.task_history),
            "worker_running": self.is_running
        }
