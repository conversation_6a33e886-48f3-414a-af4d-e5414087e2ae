#!/usr/bin/env python3
"""
Test Instagram connection and credentials
"""
import sys
import os
from instagrapi import Client
from instagrapi.exceptions import <PERSON><PERSON><PERSON><PERSON>quired, <PERSON>lient<PERSON>rror, TwoFactor<PERSON>equired, Bad<PERSON>assword, ChallengeRequired

def test_basic_connection():
    """Test basic Instagram API connection"""
    print("=== Testing Basic Instagram Connection ===")
    
    try:
        client = Client()
        print("✅ Instagram client created successfully")
        
        # Test basic API call (doesn't require login)
        try:
            # This should work without login
            print("📡 Testing basic API connectivity...")
            # Just create client - no API calls needed for basic test
            print("✅ Basic connectivity test passed")
        except Exception as e:
            print(f"❌ Basic connectivity failed: {e}")
            
    except Exception as e:
        print(f"❌ Failed to create Instagram client: {e}")
        return False
    
    return True

def test_credentials():
    """Test Instagram credentials"""
    print("\n=== Testing Instagram Credentials ===")
    
    username = "hh"
    password = "**********"
    
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    
    try:
        client = Client()
        
        # Configure client settings
        print("🔧 Configuring client settings...")
        
        # Try login
        print("🔐 Attempting login...")
        success = client.login(username, password)
        
        if success:
            print("✅ Login successful!")
            
            # Test basic API call
            try:
                user_info = client.account_info()
                print(f"✅ Account info retrieved: {user_info.username}")
                return True
            except Exception as e:
                print(f"⚠️ Login successful but API call failed: {e}")
                return True
        else:
            print("❌ Login failed - no success response")
            return False
            
    except BadPassword as e:
        print(f"❌ Invalid credentials: {e}")
        print("💡 Please check your username and password")
        return False
        
    except TwoFactorRequired as e:
        print(f"🔐 Two-factor authentication required: {e}")
        print("💡 Please disable 2FA or handle it manually")
        return False
        
    except ChallengeRequired as e:
        print(f"🚫 Challenge required: {e}")
        print("💡 Your account may be flagged. Try logging in via Instagram app/web first")
        return False
        
    except ClientError as e:
        print(f"❌ Client error: {e}")
        error_msg = str(e).lower()
        if "rate limit" in error_msg:
            print("💡 Rate limit exceeded. Wait and try again later")
        elif "login" in error_msg:
            print("💡 Login failed - check credentials or account status")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_session_handling():
    """Test session handling"""
    print("\n=== Testing Session Handling ===")
    
    session_file = "test_session.json"
    
    try:
        # Clean up any existing session
        if os.path.exists(session_file):
            os.remove(session_file)
            print("🧹 Cleaned up existing session file")
        
        client = Client()
        username = "hh"
        password = "**********"
        
        print("🔐 Testing login with session save...")
        success = client.login(username, password)
        
        if success:
            print("✅ Login successful")
            
            # Save session
            client.dump_settings(session_file)
            print(f"💾 Session saved to {session_file}")
            
            # Test session loading
            client2 = Client()
            client2.load_settings(session_file)
            
            print("📂 Testing session restore...")
            try:
                user_info = client2.account_info()
                print(f"✅ Session restored successfully: {user_info.username}")
            except Exception as e:
                print(f"❌ Session restore failed: {e}")
                
        # Clean up
        if os.path.exists(session_file):
            os.remove(session_file)
            print("🧹 Cleaned up test session file")
            
    except Exception as e:
        print(f"❌ Session test failed: {e}")

def main():
    print("Instagram Connection & Credentials Test")
    print("=" * 50)
    
    # Test basic connection
    if not test_basic_connection():
        print("❌ Basic connection failed. Exiting.")
        return
    
    # Test credentials
    if not test_credentials():
        print("❌ Credential test failed.")
        print("\n🔍 Troubleshooting Tips:")
        print("1. Verify username and password are correct")
        print("2. Try logging into Instagram web/app manually first")
        print("3. Disable 2FA temporarily if enabled")
        print("4. Check if account is locked or requires verification")
        print("5. Wait a few minutes if rate limited")
        return
    
    # Test session handling
    test_session_handling()
    
    print("\n" + "=" * 50)
    print("✅ All tests completed successfully!")
    print("Your Instagram credentials and connection are working.")

if __name__ == "__main__":
    main()
