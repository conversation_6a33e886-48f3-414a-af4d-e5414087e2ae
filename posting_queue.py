"""
Queue management for Instagram Reels Autoposter
"""
import json
import os
from pathlib import Path
from typing import List, Dict, Any, Optional


class QueueManager:
    """Manages the posting queue for videos."""

    def __init__(self, queue_file: str = 'posting_queue.json'):
        self.queue_file = queue_file
        self.queue: List[Dict[str, Any]] = []
        self.load_queue_from_file()

    def load_queue_from_file(self) -> None:
        """Load queue from JSON file if exists."""
        if os.path.exists(self.queue_file):
            try:
                with open(self.queue_file, 'r') as f:
                    self.queue = json.load(f)
                print(f"Loaded queue with {len(self.queue)} items.")
            except (json.JSONDecodeError, IOError) as e:
                print(f"Warning: Could not load queue file: {e}")
                self.queue = []
        else:
            self.queue = []

    def save_queue(self) -> None:
        """Save queue to JSON file."""
        try:
            with open(self.queue_file, 'w') as f:
                json.dump(self.queue, f, indent=4)
        except IOError as e:
            print(f"Error saving queue: {e}")

    def load_queue(self, folder_path: str) -> None:
        """Load files into queue from folder."""
        self.queue = []
        video_ext = {'.mp4', '.mov', '.avi', '.mkv', '.webm'}

        folder = Path(folder_path)
        for file_path in folder.iterdir():
            if file_path.suffix.lower() in video_ext:
                caption = self._generate_caption(file_path)
                self.queue.append({
                    'file': str(file_path),
                    'caption': caption,
                    'status': 'pending',
                    'selected': True
                })

        self.save_queue()
        print(f"Loaded {len(self.queue)} videos into queue.")

    def _generate_caption(self, file_path: Path) -> str:
        """Generate or load caption for video."""
        caption_file = file_path.with_suffix('.txt')
        if caption_file.exists():
            try:
                with open(caption_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
            except IOError:
                pass
        return f"{file_path.stem} #reels #instagram"

    def add_video(self, file_path: str, caption: Optional[str] = None) -> None:
        """Add a single video to the queue."""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Video file not found: {file_path}")

        if caption is None:
            caption = self._generate_caption(Path(file_path))

        self.queue.append({
            'file': str(file_path),
            'caption': caption,
            'status': 'pending',
            'selected': True
        })
        self.save_queue()

    def get_queue_preview(self) -> List[Dict[str, Any]]:
        """Get queue preview for GUI."""
        return self.queue.copy()

    def update_selection(self, indices: List[int], selected: bool) -> None:
        """Update selected status for items."""
        for i in indices:
            if 0 <= i < len(self.queue):
                self.queue[i]['selected'] = selected
        self.save_queue()

    def edit_caption(self, index: int, new_caption: str) -> None:
        """Edit caption for item."""
        if 0 <= index < len(self.queue):
            self.queue[index]['caption'] = new_caption
            self.save_queue()

    def skip_item(self, index: int) -> None:
        """Skip item."""
        if 0 <= index < len(self.queue):
            self.queue[index]['status'] = 'skipped'
            self.save_queue()

    def remove_item(self, index: int) -> None:
        """Remove item from queue."""
        if 0 <= index < len(self.queue):
            self.queue.pop(index)
            self.save_queue()

    def get_next_pending(self) -> Optional[Dict[str, Any]]:
        """Get next selected pending item."""
        for item in self.queue:
            if item['status'] == 'pending' and item.get('selected', True):
                return item
        return None

    def get_pending_count(self) -> int:
        """Get count of pending items."""
        return sum(1 for item in self.queue
                  if item['status'] == 'pending' and item.get('selected', True))

    def clear_completed(self) -> None:
        """Remove completed and failed items from queue."""
        self.queue = [item for item in self.queue
                     if item['status'] not in ['posted', 'failed']]
        self.save_queue()

    def reset_failed(self) -> None:
        """Reset failed items to pending."""
        for item in self.queue:
            if item['status'] == 'failed':
                item['status'] = 'pending'
        self.save_queue()

    def get_item(self, index: int) -> Optional[Dict[str, Any]]:
        """Get item at index."""
        if 0 <= index < len(self.queue):
            return self.queue[index]
        return None

    def update_item_status(self, index: int, status: str, **kwargs) -> None:
        """Update item status and additional fields."""
        if 0 <= index < len(self.queue):
            self.queue[index]['status'] = status
            self.queue[index].update(kwargs)
            self.save_queue()
