"""
Enhanced Configuration management for Instagram Reels Autoposter
"""
import json
import os
import base64
from typing import Optional, Dict, Any


class ConfigManager:
    """Handles loading and saving configuration settings with enhanced OpenRouter support."""

    def __init__(self, config_file: str = 'config.json'):
        self.config_file = config_file
        self._config = {}
        self.load_config()
        self._ensure_default_config()

    def _ensure_default_config(self) -> None:
        """Ensure default configuration values are set."""
        defaults = {
            'credentials': {
                'username': '',
                'password': '',
                'auto_login': True,
                'save_session': True
            },
            'openrouter': {
                'api_key': '',
                'default_model': 'meta-llama/llama-3.2-3b-instruct:free',
                'fallback_model': 'meta-llama/llama-3.2-1b-instruct:free',
                'temperature': 0.7,
                'max_tokens': 500,
                'cache_enabled': True
            },
            'caption_generation': {
                'default_style': 'engaging',
                'max_caption_length': 2200,
                'include_hashtags': True,
                'max_hashtags': 25,
                'auto_generate_on_scan': False,
                'bulk_generation_enabled': True
            },
            'posting': {
                'enable_scheduling': True,
                'schedule_as_draft': True,
                'default_delay_minutes': 30,
                'enable_auto_music': False,
                'enable_auto_effects': False
            },
            'ui': {
                'theme': 'modern',
                'auto_refresh_interval': 30,
                'show_notifications': True,
                'compact_mode': False
            }
        }
        
        # Merge defaults with existing config
        for section, section_defaults in defaults.items():
            if section not in self._config:
                self._config[section] = {}
            for key, default_value in section_defaults.items():
                if key not in self._config[section]:
                    self._config[section][key] = default_value

    def load_config(self) -> None:
        """Load configuration from JSON file."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    self._config = json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Warning: Could not load config file: {e}")
                self._config = {}
        else:
            self._config = {}

    def save_config(self) -> None:
        """Save configuration to JSON file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self._config, f, indent=4)
        except IOError as e:
            print(f"Error saving config: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with support for nested keys."""
        if '.' in key:
            sections = key.split('.')
            value = self._config
            for section in sections:
                if isinstance(value, dict) and section in value:
                    value = value[section]
                else:
                    return default
            return value
        return self._config.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """Set configuration value with support for nested keys."""
        if '.' in key:
            sections = key.split('.')
            config_ref = self._config
            for section in sections[:-1]:
                if section not in config_ref:
                    config_ref[section] = {}
                config_ref = config_ref[section]
            config_ref[sections[-1]] = value
        else:
            self._config[key] = value

    def update(self, updates: Dict[str, Any]) -> None:
        """Update multiple configuration values."""
        self._config.update(updates)

    # OpenRouter API properties
    @property
    def openrouter_api_key(self) -> str:
        """Get OpenRouter API key (decoded if encoded)."""
        key = self.get('openrouter.api_key', '')
        if key and key.startswith('b64:'):
            try:
                return base64.b64decode(key[4:]).decode('utf-8')
            except:
                return ''
        return key

    @openrouter_api_key.setter
    def openrouter_api_key(self, value: str):
        """Set OpenRouter API key (encoded for security)."""
        if value:
            # Encode the API key for basic security
            encoded = base64.b64encode(value.encode('utf-8')).decode('utf-8')
            self.set('openrouter.api_key', f'b64:{encoded}')
        else:
            self.set('openrouter.api_key', '')

    @property
    def openrouter_default_model(self) -> str:
        return self.get('openrouter.default_model', 'meta-llama/llama-3.2-3b-instruct:free')

    @openrouter_default_model.setter
    def openrouter_default_model(self, value: str):
        self.set('openrouter.default_model', value)

    @property
    def openrouter_fallback_model(self) -> str:
        return self.get('openrouter.fallback_model', 'meta-llama/llama-3.2-1b-instruct:free')

    @openrouter_fallback_model.setter
    def openrouter_fallback_model(self, value: str):
        self.set('openrouter.fallback_model', value)

    # Caption generation properties
    @property
    def caption_default_style(self) -> str:
        return self.get('caption_generation.default_style', 'engaging')

    @caption_default_style.setter
    def caption_default_style(self, value: str):
        self.set('caption_generation.default_style', value)

    @property
    def auto_generate_captions(self) -> bool:
        return self.get('caption_generation.auto_generate_on_scan', False)

    @auto_generate_captions.setter
    def auto_generate_captions(self, value: bool):
        self.set('caption_generation.auto_generate_on_scan', value)

    @property
    def bulk_generation_enabled(self) -> bool:
        return self.get('caption_generation.bulk_generation_enabled', True)

    @bulk_generation_enabled.setter
    def bulk_generation_enabled(self, value: bool):
        self.set('caption_generation.bulk_generation_enabled', value)

    # Posting/Scheduling properties
    @property
    def enable_scheduling(self) -> bool:
        return self.get('posting.enable_scheduling', True)

    @enable_scheduling.setter
    def enable_scheduling(self, value: bool):
        self.set('posting.enable_scheduling', value)

    @property
    def schedule_as_draft(self) -> bool:
        return self.get('posting.schedule_as_draft', True)

    @schedule_as_draft.setter
    def schedule_as_draft(self, value: bool):
        self.set('posting.schedule_as_draft', value)

    @property
    def default_delay_minutes(self) -> int:
        return self.get('posting.default_delay_minutes', 30)

    @default_delay_minutes.setter
    def default_delay_minutes(self, value: int):
        self.set('posting.default_delay_minutes', value)

    # UI properties
    @property
    def auto_refresh_interval(self) -> int:
        return self.get('ui.auto_refresh_interval', 30)

    @auto_refresh_interval.setter
    def auto_refresh_interval(self, value: int):
        self.set('ui.auto_refresh_interval', value)

    @property
    def show_notifications(self) -> bool:
        return self.get('ui.show_notifications', True)

    @show_notifications.setter
    def show_notifications(self, value: bool):
        self.set('ui.show_notifications', value)

    def get_openrouter_models(self) -> Dict[str, str]:
        """Get available OpenRouter models with descriptions."""
        return {
            'meta-llama/llama-3.2-3b-instruct:free': 'Llama 3.2 3B (Free)',
            'meta-llama/llama-3.2-1b-instruct:free': 'Llama 3.2 1B (Free)',
            'meta-llama/llama-3.1-8b-instruct:free': 'Llama 3.1 8B (Free)',
            'google/gemma-2-9b-it:free': 'Gemma 2 9B (Free)',
            'microsoft/phi-3-mini-128k-instruct:free': 'Phi-3 Mini (Free)',
            'microsoft/phi-3-medium-128k-instruct:free': 'Phi-3 Medium (Free)',
            'anthropic/claude-3-haiku': 'Claude 3 Haiku (Paid)',
            'anthropic/claude-3-sonnet': 'Claude 3 Sonnet (Paid)',
            'openai/gpt-4o-mini': 'GPT-4o Mini (Paid)',
            'openai/gpt-3.5-turbo': 'GPT-3.5 Turbo (Paid)'
        }

    def validate_config(self) -> Dict[str, Any]:
        """Validate current configuration and return status."""
        validation = {
            'valid': True,
            'issues': [],
            'warnings': []
        }

        # Check OpenRouter configuration
        if not self.openrouter_api_key:
            validation['warnings'].append('OpenRouter API key not configured')
        
        if self.openrouter_default_model not in self.get_openrouter_models():
            validation['issues'].append(f'Invalid default model: {self.openrouter_default_model}')
            validation['valid'] = False

        # Check caption generation settings
        max_length = self.get('caption_generation.max_caption_length', 2200)
        if max_length > 2200:
            validation['warnings'].append('Instagram caption limit is 2200 characters')

        return validation

    @property
    def dry_run(self) -> bool:
        return self.get('dry_run', False)

    @dry_run.setter
    def dry_run(self, value: bool):
        self.set('dry_run', value)

    @property
    def max_retries(self) -> int:
        return self.get('max_retries', 3)

    @max_retries.setter
    def max_retries(self, value: int):
        self.set('max_retries', value)

    @property
    def retry_backoff(self) -> int:
        return self.get('retry_backoff', 60)

    @retry_backoff.setter
    def retry_backoff(self, value: int):
        self.set('retry_backoff', value)

    @property
    def username(self) -> str:
        # Try credentials section first, then fallback to root level
        return self.get('credentials', {}).get('username', '') or self.get('username', '')

    @username.setter
    def username(self, value: str):
        # Set in both credentials section and root level for compatibility
        if 'credentials' not in self._config:
            self._config['credentials'] = {}
        self._config['credentials']['username'] = value
        self.set('username', value)

    @property
    def password(self) -> str:
        # Try credentials section first, then fallback to root level
        return self.get('credentials', {}).get('password', '') or self.get('password', '')

    @password.setter
    def password(self, value: str):
        # Set in both credentials section and root level for compatibility
        if 'credentials' not in self._config:
            self._config['credentials'] = {}
        self._config['credentials']['password'] = value
        self.set('password', value)

    @property
    def proxy(self) -> Optional[str]:
        return self.get('proxy', None)

    @proxy.setter
    def proxy(self, value: Optional[str]):
        self.set('proxy', value)

    @property
    def batch_delay(self) -> int:
        return self.get('batch_delay', 5)

    @batch_delay.setter
    def batch_delay(self, value: int):
        self.set('batch_delay', value)
