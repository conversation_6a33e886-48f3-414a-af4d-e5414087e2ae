#!/usr/bin/env python3
"""
Bulk Messaging Test Script for Instagram Autoposter
Demonstrates the optimized bulk messaging capabilities with Instagram APIs
"""

import sys
import time
from typing import List, <PERSON><PERSON>

# Add the project root to the path
sys.path.append('.')

from messaging import InstagramMessaging
from auth import Auth<PERSON>anager
from config import ConfigManager

def test_bulk_messaging_efficiency():
    """Test and compare the efficiency of different bulk messaging methods."""
    
    print("🚀 Instagram Bulk Messaging Efficiency Test")
    print("=" * 50)
    
    # Initialize components
    config_manager = ConfigManager()
    auth_manager = AuthManager('test_session.json', config_manager)
    
    # Create messaging instance
    messaging = InstagramMessaging(auth_manager.client, "test_account")
    
    # Test usernames (replace with real usernames for actual testing)
    test_usernames = [
        "instagram",
        "natgeo", 
        "nba",
        "nike",
        "spotify",
        "netflix",
        "amazon",
        "google",
        "microsoft",
        "apple"
    ]
    
    test_message = "Hello! This is a test message from our automation system. 🤖"
    
    # Prepare username-message pairs
    username_message_pairs = [(username, test_message) for username in test_usernames]
    
    print(f"📊 Testing bulk messaging with {len(username_message_pairs)} users")
    print()
    
    def progress_callback(message: str):
        print(f"  📈 {message}")
    
    # Get current messaging capabilities
    capabilities = messaging.get_messaging_capabilities()
    print("🔍 Current Messaging Capabilities:")
    print(f"  • Instagrapi Available: {'✅' if capabilities['instagrapi_available'] else '❌'}")
    print(f"  • Business API Available: {'✅' if capabilities['business_api_available'] else '❌'}")
    print(f"  • Preferred Method: {capabilities['preferred_method']}")
    print()
    
    # Option to configure Business API
    if not capabilities['business_api_available']:
        print("⚠️  Instagram Business API not configured.")
        print("   For optimal performance, configure Business API credentials:")
        print("   messaging.configure_business_api(access_token, business_account_id)")
        print()
    
    # Test 1: Username Resolution Efficiency
    print("🔍 Test 1: Username Resolution Speed")
    print("-" * 30)
    
    start_time = time.time()
    resolved_users = messaging.resolve_usernames_batch(test_usernames, progress_callback)
    resolution_time = time.time() - start_time
    
    successful_resolutions = len([r for r in resolved_users.values() if r is not None])
    
    print(f"  ⏱️  Resolution Time: {resolution_time:.2f} seconds")
    print(f"  ✅ Successful: {successful_resolutions}/{len(test_usernames)}")
    print(f"  📊 Speed: {len(test_usernames)/resolution_time:.1f} usernames/second")
    print()
    
    # Test 2: Cache Performance
    print("🔍 Test 2: Cache Performance (Second Run)")
    print("-" * 30)
    
    start_time = time.time()
    resolved_users_cached = messaging.resolve_usernames_batch(test_usernames, progress_callback)
    cached_resolution_time = time.time() - start_time
    
    print(f"  ⏱️  Cached Resolution Time: {cached_resolution_time:.2f} seconds")
    print(f"  📈 Speed Improvement: {resolution_time/cached_resolution_time:.1f}x faster")
    print()
    
    # Test 3: Bulk Messaging Simulation (DRY RUN)
    print("🔍 Test 3: Bulk Messaging Simulation (DRY RUN)")
    print("-" * 30)
    print("  ⚠️  Note: This is a simulation - no actual messages will be sent")
    print()
    
    # Simulate bulk messaging without actually sending
    start_time = time.time()
    
    if capabilities['business_api_available']:
        print("  🎯 Would use Instagram Business API for optimal performance")
        estimated_time_per_message = 0.8  # seconds (based on 4800/hour rate limit)
    else:
        print("  🎯 Would use instagrapi method")
        estimated_time_per_message = 2.0  # seconds (conservative estimate)
    
    estimated_total_time = len(username_message_pairs) * estimated_time_per_message
    
    print(f"  📊 Estimated messaging time: {estimated_total_time:.1f} seconds")
    print(f"  📈 Estimated throughput: {3600/estimated_time_per_message:.0f} messages/hour")
    print()
    
    # Display cache statistics
    cache_stats = messaging.user_cache.get_cache_stats()
    print("📊 Cache Statistics:")
    print(f"  • Total Cached Users: {cache_stats['total_cached']}")
    print(f"  • Cache File: {cache_stats['cache_file']}")
    print(f"  • Cache Expiry: {cache_stats['expiry_hours']} hours")
    print()
    
    # Display rate limiting information
    print("⏱️  Rate Limiting Status:")
    for method, limits in capabilities['rate_limits'].items():
        print(f"  • {method.title()}: {limits}")
    print()
    
    print("✅ Bulk messaging efficiency test completed!")
    return resolved_users

def demonstrate_business_api_setup():
    """Demonstrate how to set up Instagram Business API."""
    
    print("\n🔧 Instagram Business API Setup Guide")
    print("=" * 50)
    
    print("""
To enable the most efficient bulk messaging, configure Instagram Business API:

1. Create a Facebook Developer Account:
   • Go to https://developers.facebook.com/
   • Create a new app with Instagram messaging permissions

2. Get your credentials:
   • Access Token (with instagram_messaging permission)
   • Instagram Business Account ID

3. Configure in your code:
   ```python
   messaging.configure_business_api(
       access_token="YOUR_ACCESS_TOKEN",
       business_account_id="YOUR_BUSINESS_ACCOUNT_ID"
   )
   ```

4. Benefits of Business API:
   • 4800 requests/hour (vs 500 with instagrapi)
   • Batch username resolution (50 users at once)
   • Better reliability and fewer blocks
   • Official Instagram support

5. Messaging rates:
   • Business API: ~1.3 messages/second (4800/hour)
   • Instagrapi: ~0.5 messages/second (conservative)
   """)

def example_usage():
    """Show example usage of the optimized bulk messaging."""
    
    print("\n💡 Example Usage")
    print("=" * 50)
    
    example_code = '''
# Example: Send bulk messages efficiently
from messaging import InstagramMessaging
from auth import AuthManager
from config import ConfigManager

# Initialize
config = ConfigManager()
auth = AuthManager('session.json', config)
messaging = InstagramMessaging(auth.client, "my_account")

# Optional: Configure Business API for better performance
messaging.configure_business_api(
    access_token="YOUR_ACCESS_TOKEN",
    business_account_id="YOUR_BUSINESS_ACCOUNT_ID"
)

# Prepare messages
username_message_pairs = [
    ("user1", "Hello user1! 👋"),
    ("user2", "Hello user2! 👋"),
    ("user3", "Hello user3! 👋"),
    # ... more users
]

# Send bulk messages with progress tracking
def progress_update(message):
    print(f"Progress: {message}")

results = messaging.send_bulk_messages_optimized(
    username_message_pairs, 
    progress_callback=progress_update
)

# Check results
print(f"Sent: {results['messages_sent']}")
print(f"Failed: {results['messages_failed']}")
print(f"Success rate: {results['success_rate']:.1f}%")
print(f"Method used: {results['method']}")
'''
    
    print(example_code)

if __name__ == "__main__":
    try:
        # Run the efficiency test
        test_bulk_messaging_efficiency()
        
        # Show setup guide
        demonstrate_business_api_setup()
        
        # Show example usage
        example_usage()
        
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
