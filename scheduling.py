"""
Instagram Video Scheduling System
Provides functionality to schedule videos for later posting with enhanced draft support
"""
import json
import os
import time
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, List, Callable
import logging

logger = logging.getLogger(__name__)


class ScheduleManager:
    """Manages scheduled posts and automatic posting with draft support."""
    
    def __init__(self, config_manager=None, account_manager=None):
        self.config_manager = config_manager
        self.account_manager = account_manager
        self.scheduled_posts = []
        self.schedule_file = "scheduled_posts.json"
        self.is_running = False
        self.scheduler_thread = None
        self.callbacks = []
        
        # Scheduling options
        self.scheduling_options = {
            'immediately': 'Post Immediately',
            'draft': 'Save as Draft (Recommended)',
            'schedule_delay': 'Schedule for Later',
            'optimal_time': 'Optimal Posting Time'
        }
        
        # Load existing scheduled posts
        self.load_scheduled_posts()
    
    def load_scheduled_posts(self) -> None:
        """Load scheduled posts from file."""
        try:
            if os.path.exists(self.schedule_file):
                with open(self.schedule_file, 'r') as f:
                    data = json.load(f)
                    self.scheduled_posts = data.get('scheduled_posts', [])
                    logger.info(f"Loaded {len(self.scheduled_posts)} scheduled posts")
        except Exception as e:
            logger.error(f"Failed to load scheduled posts: {e}")
            self.scheduled_posts = []
    
    def save_scheduled_posts(self) -> None:
        """Save scheduled posts to file."""
        try:
            data = {
                'scheduled_posts': self.scheduled_posts,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.schedule_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save scheduled posts: {e}")
    
    def schedule_video(self, video_path: str, caption: str, account_id: str,
                      schedule_type: str = 'draft', schedule_time: Optional[datetime] = None,
                      additional_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Schedule a video for posting with multiple options."""
        
        # Validate inputs
        if not os.path.exists(video_path):
            return {"success": False, "error": "Video file not found"}
        
        if not account_id or account_id not in self.account_manager.accounts:
            return {"success": False, "error": "Invalid account ID"}
        
        # Create scheduled post entry
        scheduled_post = {
            'id': f"schedule_{int(time.time())}_{len(self.scheduled_posts)}",
            'video_path': video_path,
            'caption': caption,
            'account_id': account_id,
            'schedule_type': schedule_type,
            'created_at': datetime.now().isoformat(),
            'status': 'pending',
            'attempts': 0,
            'max_attempts': 3,
            'additional_options': additional_options or {}
        }
        
        # Handle different scheduling types
        if schedule_type == 'immediately':
            scheduled_post['schedule_time'] = datetime.now().isoformat()
            scheduled_post['post_immediately'] = True
        elif schedule_type == 'draft':
            # For draft, we prepare the post but don't schedule it for automatic posting
            scheduled_post['schedule_time'] = None
            scheduled_post['save_as_draft'] = True
            scheduled_post['draft_prepared'] = True
        elif schedule_type == 'schedule_delay':
            if not schedule_time:
                # Default to 30 minutes from now
                default_delay = self.config_manager.default_delay_minutes if self.config_manager else 30
                schedule_time = datetime.now() + timedelta(minutes=default_delay)
            scheduled_post['schedule_time'] = schedule_time.isoformat()
        elif schedule_type == 'optimal_time':
            # Calculate optimal posting time (simplified - can be enhanced)
            optimal_time = self._calculate_optimal_time()
            scheduled_post['schedule_time'] = optimal_time.isoformat()
        
        # Add to scheduled posts
        self.scheduled_posts.append(scheduled_post)
        self.save_scheduled_posts()
        
        # Notify callbacks
        self._notify_callbacks('post_scheduled', scheduled_post)
        
        logger.info(f"Scheduled post {scheduled_post['id']} for account {account_id}")
        
        return {
            "success": True,
            "schedule_id": scheduled_post['id'],
            "schedule_type": schedule_type,
            "message": self._get_schedule_message(schedule_type, schedule_time)
        }
    
    def _get_schedule_message(self, schedule_type: str, schedule_time: Optional[datetime] = None) -> str:
        """Get user-friendly message about scheduling."""
        if schedule_type == 'immediately':
            return "Video will be posted immediately"
        elif schedule_type == 'draft':
            return "Video prepared as draft. You can edit and post manually using Instagram app"
        elif schedule_type == 'schedule_delay':
            if schedule_time:
                return f"Video scheduled for {schedule_time.strftime('%Y-%m-%d %H:%M')}"
            return "Video scheduled for later"
        elif schedule_type == 'optimal_time':
            return "Video scheduled for optimal posting time based on engagement patterns"
        return "Video scheduled successfully"
    
    def _calculate_optimal_time(self) -> datetime:
        """Calculate optimal posting time based on engagement patterns."""
        now = datetime.now()
        
        # Simple heuristic: Post during high-engagement hours
        # Can be enhanced with actual analytics data
        
        # If it's currently between 6 PM and 9 PM, post now
        if 18 <= now.hour <= 21:
            return now + timedelta(minutes=5)
        
        # Otherwise, schedule for next 7 PM
        next_optimal = now.replace(hour=19, minute=0, second=0, microsecond=0)
        if next_optimal <= now:
            next_optimal += timedelta(days=1)
        
        return next_optimal
    
    def schedule_bulk_videos(self, video_items: List[Dict[str, Any]], account_id: str,
                           schedule_type: str = 'draft', start_time: Optional[datetime] = None,
                           interval_minutes: int = 30) -> Dict[str, Any]:
        """Schedule multiple videos with staggered timing."""
        
        if not video_items:
            return {"success": False, "error": "No videos provided"}
        
        scheduled_ids = []
        current_time = start_time or datetime.now()
        
        for i, item in enumerate(video_items):
            try:
                # Calculate schedule time for this video
                if schedule_type in ['schedule_delay', 'optimal_time']:
                    item_schedule_time = current_time + timedelta(minutes=i * interval_minutes)
                else:
                    item_schedule_time = None
                
                # Schedule the video
                result = self.schedule_video(
                    video_path=item['file'],
                    caption=item.get('caption', ''),
                    account_id=account_id,
                    schedule_type=schedule_type,
                    schedule_time=item_schedule_time
                )
                
                if result['success']:
                    scheduled_ids.append(result['schedule_id'])
                else:
                    logger.error(f"Failed to schedule {item['file']}: {result['error']}")
                    
            except Exception as e:
                logger.error(f"Error scheduling {item.get('file', 'unknown')}: {e}")
        
        return {
            "success": True,
            "scheduled_count": len(scheduled_ids),
            "scheduled_ids": scheduled_ids,
            "total_videos": len(video_items)
        }
    
    def prepare_draft_post(self, video_path: str, caption: str, account_id: str) -> Dict[str, Any]:
        """Prepare a post as draft for manual editing in Instagram app."""
        
        try:
            account = self.account_manager.get_account(account_id)
            if not account:
                return {"success": False, "error": "Account not found"}
            
            if not account.is_logged_in:
                return {"success": False, "error": "Account not logged in"}
            
            # Create a special queue item marked as draft
            draft_item = {
                'file': video_path,
                'caption': caption,
                'status': 'draft_prepared',
                'draft_mode': True,
                'account_id': account_id,
                'created_at': datetime.now().isoformat(),
                'instructions': 'This video is prepared for manual posting. Edit caption/add music in Instagram app before posting.'
            }
            
            # Add to account's queue as draft
            account.queue_manager.queue.append(draft_item)
            account.queue_manager.save_queue()
            
            return {
                "success": True,
                "message": "Video prepared as draft. Edit and post manually using Instagram app",
                "draft_item": draft_item
            }
            
        except Exception as e:
            logger.error(f"Failed to prepare draft post: {e}")
            return {"success": False, "error": str(e)}
    
    def get_scheduled_posts(self, account_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get scheduled posts, optionally filtered by account."""
        if account_id:
            return [post for post in self.scheduled_posts if post['account_id'] == account_id]
        return self.scheduled_posts.copy()
    
    def cancel_scheduled_post(self, schedule_id: str) -> bool:
        """Cancel a scheduled post."""
        try:
            for i, post in enumerate(self.scheduled_posts):
                if post['id'] == schedule_id:
                    post['status'] = 'cancelled'
                    post['cancelled_at'] = datetime.now().isoformat()
                    self.save_scheduled_posts()
                    self._notify_callbacks('post_cancelled', post)
                    logger.info(f"Cancelled scheduled post {schedule_id}")
                    return True
            return False
        except Exception as e:
            logger.error(f"Failed to cancel scheduled post: {e}")
            return False
    
    def start_scheduler(self) -> bool:
        """Start the background scheduler."""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return False
        
        try:
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            logger.info("Scheduler started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            self.is_running = False
            return False
    
    def stop_scheduler(self) -> None:
        """Stop the background scheduler."""
        self.is_running = False
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        logger.info("Scheduler stopped")
    
    def _scheduler_loop(self) -> None:
        """Background scheduler loop."""
        logger.info("Scheduler loop started")
        
        while self.is_running:
            try:
                self._process_scheduled_posts()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                time.sleep(60)
        
        logger.info("Scheduler loop ended")
    
    def _process_scheduled_posts(self) -> None:
        """Process scheduled posts that are ready."""
        current_time = datetime.now()
        
        for post in self.scheduled_posts:
            try:
                if post['status'] != 'pending':
                    continue
                
                # Skip draft posts
                if post.get('save_as_draft'):
                    continue
                
                # Check if it's time to post
                schedule_time_str = post.get('schedule_time')
                if not schedule_time_str:
                    continue
                
                schedule_time = datetime.fromisoformat(schedule_time_str)
                
                if current_time >= schedule_time:
                    self._execute_scheduled_post(post)
                    
            except Exception as e:
                logger.error(f"Error processing scheduled post {post.get('id', 'unknown')}: {e}")
    
    def _execute_scheduled_post(self, post: Dict[str, Any]) -> None:
        """Execute a scheduled post."""
        try:
            account_id = post['account_id']
            account = self.account_manager.get_account(account_id)
            
            if not account:
                post['status'] = 'failed'
                post['error'] = 'Account not found'
                self.save_scheduled_posts()
                return
            
            if not account.is_logged_in:
                post['status'] = 'failed'
                post['error'] = 'Account not logged in'
                self.save_scheduled_posts()
                return
            
            # Create posting item
            posting_item = {
                'file': post['video_path'],
                'caption': post['caption'],
                'status': 'pending',
                'scheduled_post_id': post['id']
            }
            
            # Post the video
            def posting_callback(level: str, message: str):
                logger.info(f"Scheduled post {post['id']}: {message}")
                self._notify_callbacks('posting_update', {'post_id': post['id'], 'level': level, 'message': message})
            
            account.posting_manager.post_reel(posting_item, callback=posting_callback)
            
            # Update status
            post['status'] = 'posted'
            post['posted_at'] = datetime.now().isoformat()
            post['attempts'] += 1
            
            logger.info(f"Successfully executed scheduled post {post['id']}")
            self._notify_callbacks('post_executed', post)
            
        except Exception as e:
            post['status'] = 'failed'
            post['error'] = str(e)
            post['attempts'] += 1
            post['failed_at'] = datetime.now().isoformat()
            
            logger.error(f"Failed to execute scheduled post {post['id']}: {e}")
            self._notify_callbacks('post_failed', post)
        
        self.save_scheduled_posts()
    
    def add_callback(self, callback: Callable) -> None:
        """Add a callback for scheduler events."""
        self.callbacks.append(callback)
    
    def _notify_callbacks(self, event_type: str, data: Dict[str, Any]) -> None:
        """Notify all callbacks of an event."""
        for callback in self.callbacks:
            try:
                callback(event_type, data)
            except Exception as e:
                logger.error(f"Error in scheduler callback: {e}")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """Get scheduler status and statistics."""
        pending_posts = len([p for p in self.scheduled_posts if p['status'] == 'pending'])
        posted_posts = len([p for p in self.scheduled_posts if p['status'] == 'posted'])
        failed_posts = len([p for p in self.scheduled_posts if p['status'] == 'failed'])
        draft_posts = len([p for p in self.scheduled_posts if p.get('save_as_draft')])
        
        return {
            'is_running': self.is_running,
            'total_scheduled': len(self.scheduled_posts),
            'pending_posts': pending_posts,
            'posted_posts': posted_posts,
            'failed_posts': failed_posts,
            'draft_posts': draft_posts,
            'next_post_time': self._get_next_post_time()
        }
    
    def _get_next_post_time(self) -> Optional[str]:
        """Get the next scheduled post time."""
        pending_posts = [p for p in self.scheduled_posts if p['status'] == 'pending' and p.get('schedule_time')]
        
        if not pending_posts:
            return None
        
        next_post = min(pending_posts, key=lambda p: p['schedule_time'])
        return next_post['schedule_time']
    
    def cleanup_old_posts(self, days_old: int = 7) -> int:
        """Clean up old completed/failed posts."""
        cutoff_time = datetime.now() - timedelta(days=days_old)
        initial_count = len(self.scheduled_posts)
        
        self.scheduled_posts = [
            post for post in self.scheduled_posts
            if (post['status'] == 'pending' or 
                datetime.fromisoformat(post['created_at']) > cutoff_time)
        ]
        
        cleaned_count = initial_count - len(self.scheduled_posts)
        
        if cleaned_count > 0:
            self.save_scheduled_posts()
            logger.info(f"Cleaned up {cleaned_count} old scheduled posts")
        
        return cleaned_count
