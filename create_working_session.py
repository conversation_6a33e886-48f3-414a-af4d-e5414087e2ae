#!/usr/bin/env python3
"""
Create a working session for messaging without login
This bypasses the IP blacklist issue for testing messaging functionality
"""
import sys
import os
import json
import uuid
from datetime import datetime
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.getcwd())

def create_mock_session():
    """Create a mock session file that can be used for messaging testing"""
    print("=== Creating Mock Session for Messaging ===")
    
    try:
        # Create accounts directory
        account_dir = Path("accounts/hh122.11")
        account_dir.mkdir(parents=True, exist_ok=True)
        
        # Create a realistic session structure
        mock_session = {
            "device_settings": {
                "app_version": "283.**********",
                "android_version": 29,
                "android_release": "10",
                "dpi": "480dpi",
                "resolution": "1080x1920",
                "manufacturer": "OnePlus",
                "device": "OnePlus6",
                "model": "OnePlus6",
                "cpu": "qcom"
            },
            "user_agent": "Instagram 283.********** Android (29/10; 480dpi; 1080x1920; OnePlus; OnePlus6; OnePlus6; qcom; en_US; *********)",
            "device_id": f"android-{uuid.uuid4().hex[:16]}",
            "uuid": str(uuid.uuid4()),
            "phone_id": str(uuid.uuid4()),
            "session_id": str(uuid.uuid4()),
            "client_session_id": str(uuid.uuid4()),
            "advertising_id": str(uuid.uuid4()),
            "request_id": str(uuid.uuid4()),
            "tray_session_id": str(uuid.uuid4()),
            
            # Mock user data for testing
            "user_id": "*********",
            "username": "hh",
            "full_name": "Test User",
            "is_verified": False,
            "is_private": False,
            
            # Session metadata
            "session_metadata": {
                "created_at": datetime.now().isoformat(),
                "session_type": "mock_for_messaging",
                "version": "3.0"
            },
            
            # Mock authentication tokens (for structure only)
            "authorization_data": {
                "ds_user_id": "*********",
                "sessionid": f"mock_session_{uuid.uuid4().hex[:16]}",
                "csrftoken": f"mock_csrf_{uuid.uuid4().hex[:16]}"
            }
        }
        
        # Save session file
        session_file = account_dir / "session.json"
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(mock_session, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Created mock session: {session_file}")
        return str(session_file)
        
    except Exception as e:
        print(f"❌ Failed to create mock session: {e}")
        return None

def create_messaging_config():
    """Create configuration for messaging functionality"""
    print("\n=== Creating Messaging Configuration ===")
    
    try:
        # Update account config for messaging
        config_file = "accounts/hh122.11/config.json"
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}
        
        # Add messaging-specific configuration
        config.update({
            "messaging": {
                "enabled": True,
                "rate_limit_per_hour": 100,
                "delay_between_messages": 2,
                "auto_retry_failed": True,
                "max_retries": 3,
                "bulk_mode_enabled": True,
                "user_cache_enabled": True,
                "business_api_enabled": False
            },
            "session": {
                "auto_refresh": True,
                "refresh_interval_hours": 6,
                "mock_mode": True,
                "bypass_login": True
            }
        })
        
        # Save updated config
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Updated messaging config: {config_file}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create messaging config: {e}")
        return False

def create_sample_users():
    """Create sample users for testing messaging"""
    print("\n=== Creating Sample Users ===")
    
    try:
        # Create user cache directory
        cache_dir = Path("accounts/hh122.11")
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Sample users for testing
        sample_users = {
            "test_user1": {
                "user_id": "*********",
                "username": "test_user1",
                "cached_at": datetime.now().isoformat()
            },
            "test_user2": {
                "user_id": "*********", 
                "username": "test_user2",
                "cached_at": datetime.now().isoformat()
            },
            "test_user3": {
                "user_id": "*********",
                "username": "test_user3", 
                "cached_at": datetime.now().isoformat()
            },
            "demo_account": {
                "user_id": "*********",
                "username": "demo_account",
                "cached_at": datetime.now().isoformat()
            },
            "sample_user": {
                "user_id": "*********",
                "username": "sample_user",
                "cached_at": datetime.now().isoformat()
            }
        }
        
        # Save user cache
        cache_file = cache_dir / "user_cache.json"
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(sample_users, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Created sample users cache: {cache_file}")
        print(f"   - {len(sample_users)} sample users available for testing")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create sample users: {e}")
        return False

def create_message_templates():
    """Create sample message templates"""
    print("\n=== Creating Message Templates ===")
    
    try:
        templates = {
            "greeting": {
                "name": "Friendly Greeting",
                "message": "Hello! 👋 Hope you're having a great day!",
                "category": "greetings"
            },
            "follow_up": {
                "name": "Follow Up",
                "message": "Hi there! Just wanted to follow up on our previous conversation. 😊",
                "category": "follow_up"
            },
            "promotion": {
                "name": "Product Promotion",
                "message": "Check out our latest products! 🛍️ Special discount available now!",
                "category": "marketing"
            },
            "thank_you": {
                "name": "Thank You",
                "message": "Thank you so much for your support! 🙏 It means a lot to us!",
                "category": "appreciation"
            },
            "collaboration": {
                "name": "Collaboration Inquiry",
                "message": "Hi! I love your content! 💫 Would you be interested in collaborating?",
                "category": "business"
            }
        }
        
        # Save templates
        templates_file = "message_templates.json"
        with open(templates_file, 'w', encoding='utf-8') as f:
            json.dump(templates, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Created message templates: {templates_file}")
        print(f"   - {len(templates)} templates available")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create message templates: {e}")
        return False

def update_account_status():
    """Update account status to show as logged in for messaging"""
    print("\n=== Updating Account Status ===")
    
    try:
        # Update main accounts.json
        accounts_file = "accounts.json"
        
        if os.path.exists(accounts_file):
            with open(accounts_file, 'r', encoding='utf-8') as f:
                accounts_data = json.load(f)
        else:
            accounts_data = {"accounts": [], "active_account": ""}
        
        # Update account status
        for account in accounts_data.get('accounts', []):
            if account.get('name') == 'hh122.11':
                account['last_active'] = datetime.now().isoformat()
                account['messaging_enabled'] = True
                account['session_valid'] = True
                break
        
        # Save updated accounts
        with open(accounts_file, 'w', encoding='utf-8') as f:
            json.dump(accounts_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Updated account status: {accounts_file}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update account status: {e}")
        return False

def show_usage_instructions():
    """Show instructions for using messaging functionality"""
    print("\n" + "=" * 60)
    print("🎯 MESSAGING FUNCTIONALITY READY!")
    print("=" * 60)
    
    print("\n📱 HOW TO USE MESSAGING:")
    print("1. Restart the application")
    print("2. Select account 'hh122.11'")
    print("3. Go to 'Send Messages' tab")
    print("4. Add sample usernames:")
    print("   - test_user1")
    print("   - test_user2") 
    print("   - demo_account")
    print("   - sample_user")
    print("5. Choose a message template or write custom message")
    print("6. Click 'Send Messages' to test")
    
    print("\n✅ FEATURES AVAILABLE:")
    print("- ✅ User discovery and caching")
    print("- ✅ Message templates")
    print("- ✅ Bulk messaging")
    print("- ✅ Rate limiting")
    print("- ✅ Progress tracking")
    print("- ✅ Message history")
    print("- ✅ Scheduling (queue)")
    
    print("\n⚠️ IMPORTANT NOTES:")
    print("- This is a MOCK session for testing messaging UI")
    print("- Messages won't actually be sent to Instagram")
    print("- All functionality will work except actual sending")
    print("- Perfect for testing and development")
    
    print("\n🔧 TO ENABLE REAL MESSAGING:")
    print("1. Fix the IP blacklist issue (wait 30-60 minutes)")
    print("2. Login with real Instagram credentials")
    print("3. Replace mock session with real session")
    
    print("\n🚀 READY TO TEST MESSAGING INTERFACE!")

def main():
    print("Instagram Messaging Session Creator")
    print("=" * 50)
    
    # Create all necessary components
    session_created = create_mock_session()
    config_created = create_messaging_config()
    users_created = create_sample_users()
    templates_created = create_message_templates()
    status_updated = update_account_status()
    
    if all([session_created, config_created, users_created, templates_created, status_updated]):
        show_usage_instructions()
        return True
    else:
        print("\n❌ SETUP FAILED!")
        print("Some components could not be created.")
        print("Check the errors above and try again.")
        return False

if __name__ == "__main__":
    main()
